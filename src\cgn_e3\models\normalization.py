"""
E(3)-equivariant normalization layers.

This module contains normalization layers that preserve E(3) equivariance
for both scalar and vector features.
"""

import torch
import torch.nn as nn


class E3LayerNorm(nn.Module):
    """
    E(3)-equivariant layer normalization.
    
    Normalizes scalar and vector features while preserving E(3) equivariance.
    For scalar features, standard layer normalization is applied.
    For vector features, only the magnitudes are normalized to preserve
    directional information.
    
    Args:
        channels (int): Number of feature channels
        scalar_only (bool): If True, only normalize scalar features
    """
    
    def __init__(self, channels, scalar_only=False):
        super().__init__()
        self.channels = channels
        self.scalar_only = scalar_only

        # Learnable parameters for scalar normalization
        self.scalar_scale = nn.Parameter(torch.ones(channels))
        self.scalar_bias = nn.Parameter(torch.zeros(channels))

        if not scalar_only:
            # For vector normalization (only scale, no bias to preserve equivariance)
            self.vector_scale = nn.Parameter(torch.ones(channels))

    def forward(self, x_scalar, x_vector=None):
        """
        Apply E(3)-equivariant layer normalization.
        
        Args:
            x_scalar (torch.Tensor): Scalar features [batch_size, channels]
            x_vector (torch.Tensor, optional): Vector features [batch_size, channels, 3]
            
        Returns:
            tuple: Normalized (x_scalar, x_vector) features
        """
        # Normalize scalar features (invariant)
        mean = x_scalar.mean(dim=1, keepdim=True)
        var = x_scalar.var(dim=1, keepdim=True, unbiased=False)
        x_scalar = (x_scalar - mean) / (var + 1e-5).sqrt()
        x_scalar = (x_scalar * self.scalar_scale.view(1, -1) + 
                   self.scalar_bias.view(1, -1))

        # Normalize vector features if provided (equivariant)
        if x_vector is not None and not self.scalar_only:
            # Compute vector norms (invariant)
            vec_norm = torch.norm(x_vector, dim=2, keepdim=True)
            vec_mean = vec_norm.mean(dim=1, keepdim=True)
            vec_var = vec_norm.var(dim=1, keepdim=True, unbiased=False)

            # Scale vectors by normalized norms (equivariant)
            scale = ((vec_norm / (vec_var + 1e-5).sqrt()) * 
                    self.vector_scale.view(1, -1, 1))
            x_vector = x_vector * scale

        return x_scalar, x_vector
