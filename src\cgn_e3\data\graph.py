"""
Graph data structure for crystal materials.

This module contains the Graph class that handles the conversion of raw
crystal data into a format suitable for graph neural networks.
"""

import numpy as np
import torch


class Graph:
    """
    A graph representation of crystal structure data.
    
    This class processes raw crystal data including node features, coordinates,
    neighbor information, and bond lengths to create edge indices and attributes
    suitable for PyTorch Geometric.
    
    Args:
        graph_data (dict): Dictionary containing:
            - node_features: Atomic numbers or node features
            - type_counts: Count of each edge type
            - neighbor_counts: Number of neighbors for each node per edge type
            - neighbors: Neighbor indices
            - bond_lengths: Distances between connected atoms
            - cart_coords: Cartesian coordinates of atoms
    """
    
    def __init__(self, graph_data):
        try:
            self.nodes = graph_data['node_features']
            self.type_counts = graph_data['type_counts']
            self.neighbor_counts = graph_data['neighbor_counts']
            self.neighbors = graph_data['neighbors']
            self.bond_lengths = graph_data['bond_lengths']
            self.cart_coords = graph_data['cart_coords']

            # Convert to numpy arrays for consistent handling
            self.nodes = np.array(self.nodes)
            self.type_counts = np.array(self.type_counts)
            self.neighbor_counts = np.array(self.neighbor_counts)
            self.neighbors = np.array(self.neighbors)
            self.bond_lengths = np.array(self.bond_lengths)
            self.cart_coords = np.array(self.cart_coords)

        except KeyError as e:
            raise ValueError(f"Missing required graph data field: {str(e)}")

        # Validate data consistency
        self._validate_data()
        
        # Create edge attributes and indices
        self.edge_attr = self._create_edge_attributes()
        self.edge_index = self._create_edge_index()

    def _validate_data(self):
        """Validate the consistency of input data."""
        if len(self.nodes) != len(self.cart_coords):
            raise ValueError(
                f"Number of nodes ({len(self.nodes)}) doesn't match "
                f"coordinate count ({len(self.cart_coords)})"
            )
        
        if self.cart_coords.shape[1] != 3:
            raise ValueError("Coordinates must be 3-dimensional")
        
        if len(self.bond_lengths) != len(self.neighbors):
            raise ValueError(
                f"Bond lengths count ({len(self.bond_lengths)}) must match "
                f"neighbor count ({len(self.neighbors)})"
            )

    def _create_edge_attributes(self):
        """
        Create edge attributes with bond lengths and types.
        
        Returns:
            torch.Tensor: Edge attributes with shape [num_edges, 2]
                         containing [bond_length, edge_type]
        """
        edge_types = []
        for edge_type, count in enumerate(self.type_counts):
            edge_types.extend([edge_type] * count)

        if len(edge_types) != len(self.bond_lengths):
            raise ValueError(
                f"Edge type count ({len(edge_types)}) doesn't match "
                f"bond lengths count ({len(self.bond_lengths)})"
            )

        return torch.tensor(
            np.column_stack([self.bond_lengths, edge_types]),
            dtype=torch.float32
        )

    def _create_edge_index(self):
        """
        Create edge index with source and target nodes.
        
        Returns:
            torch.Tensor: Edge index with shape [2, num_edges]
                         containing [source_nodes, target_nodes]
        """
        # Create edge sources
        edge_sources = []
        num_edge_labels = len(self.type_counts)
        neighbor_counts = self.neighbor_counts.reshape(num_edge_labels, -1)

        for edge_type in range(num_edge_labels):
            for node_idx, count in enumerate(neighbor_counts[edge_type]):
                edge_sources.extend([node_idx] * count)

        # Create edge targets
        edge_targets = []
        start_idx = 0
        for count in self.type_counts:
            end_idx = start_idx + count
            edge_targets.extend(self.neighbors[start_idx:end_idx])
            start_idx = end_idx

        # Validate edge consistency
        if len(edge_sources) != len(edge_targets):
            raise ValueError(
                f"Edge sources count ({len(edge_sources)}) and "
                f"targets count ({len(edge_targets)}) mismatch"
            )
        
        if len(edge_sources) != len(self.bond_lengths):
            raise ValueError(
                f"Edge count ({len(edge_sources)}) doesn't match "
                f"bond length count ({len(self.bond_lengths)})"
            )

        return torch.tensor(
            [edge_sources, edge_targets], 
            dtype=torch.long
        ).contiguous()
