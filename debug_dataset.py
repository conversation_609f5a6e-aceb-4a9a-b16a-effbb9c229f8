#!/usr/bin/env python3
"""
Debug script to examine the dataset structure and identify the issue.
"""

import os
import json
import numpy as np
import pandas as pd

def examine_dataset_structure():
    """Examine the structure of the dataset files."""
    print("Examining dataset structure...")
    
    # Check NPZ file
    npz_path = "BandgapTargets.npz"
    if os.path.exists(npz_path):
        print(f"\n✅ Found NPZ file: {npz_path}")
        try:
            with np.load(npz_path, allow_pickle=True) as data:
                print(f"NPZ file keys: {list(data.keys())}")
                
                if 'graph_dict' in data:
                    graph_dict = data['graph_dict'].item()
                    print(f"Number of graphs: {len(graph_dict)}")
                    
                    # Examine first graph
                    first_key = list(graph_dict.keys())[0]
                    first_graph = graph_dict[first_key]
                    print(f"\nFirst graph key: {first_key}")
                    print(f"First graph keys: {list(first_graph.keys())}")
                    
                    # Check each key in detail
                    for key, value in first_graph.items():
                        if isinstance(value, np.ndarray):
                            print(f"  {key}: numpy array, shape={value.shape}, dtype={value.dtype}")
                        elif isinstance(value, list):
                            print(f"  {key}: list, length={len(value)}")
                            if len(value) > 0:
                                print(f"    First few elements: {value[:5]}")
                        else:
                            print(f"  {key}: {type(value)}, value={value}")
                    
        except Exception as e:
            print(f"❌ Error reading NPZ file: {e}")
    else:
        print(f"❌ NPZ file not found: {npz_path}")
    
    # Check JSON config file
    config_path = "BandgapTargets_config.json"
    if os.path.exists(config_path):
        print(f"\n✅ Found config file: {config_path}")
        try:
            with open(config_path) as f:
                config = json.load(f)
            print(f"Config keys: {list(config.keys())}")
            
            if 'atomic_numbers' in config:
                print(f"Atomic numbers: {config['atomic_numbers'][:10]}...")  # First 10
            if 'node_vectors' in config:
                node_vectors = np.array(config['node_vectors'])
                print(f"Node vectors shape: {node_vectors.shape}")
                
        except Exception as e:
            print(f"❌ Error reading config file: {e}")
    else:
        print(f"❌ Config file not found: {config_path}")
    
    # Check CSV file
    csv_path = "BandgapTargets.csv"
    if os.path.exists(csv_path):
        print(f"\n✅ Found CSV file: {csv_path}")
        try:
            df = pd.read_csv(csv_path)
            print(f"CSV shape: {df.shape}")
            print(f"CSV columns: {list(df.columns)}")
            
            if 'e_form' in df.columns:
                print(f"e_form statistics:")
                print(f"  Mean: {df['e_form'].mean():.4f}")
                print(f"  Std: {df['e_form'].std():.4f}")
                print(f"  Min: {df['e_form'].min():.4f}")
                print(f"  Max: {df['e_form'].max():.4f}")
                
        except Exception as e:
            print(f"❌ Error reading CSV file: {e}")
    else:
        print(f"❌ CSV file not found: {csv_path}")

def test_simple_loading():
    """Test simple loading without the full class."""
    print("\n" + "="*50)
    print("Testing simple dataset loading...")
    
    try:
        # Load NPZ
        with np.load("BandgapTargets.npz", allow_pickle=True) as data:
            graph_dict = data['graph_dict'].item()
            
        # Load config
        with open("BandgapTargets_config.json") as f:
            config = json.load(f)
            
        # Load CSV
        df = pd.read_csv("BandgapTargets.csv")
        
        print(f"✅ Successfully loaded all files")
        print(f"Graphs: {len(graph_dict)}")
        print(f"Targets: {len(df)}")
        
        # Test accessing first graph
        first_key = list(graph_dict.keys())[0]
        first_graph = graph_dict[first_key]
        
        print(f"\nTesting first graph access:")
        print(f"Graph keys: {list(first_graph.keys())}")
        
        # Try to identify node information
        possible_node_keys = ['nodes', 'node_features', 'atomic_numbers', 'atom_types']
        found_node_key = None
        
        for key in possible_node_keys:
            if key in first_graph:
                found_node_key = key
                print(f"✅ Found node information in key: '{key}'")
                node_data = first_graph[key]
                print(f"   Type: {type(node_data)}")
                if hasattr(node_data, 'shape'):
                    print(f"   Shape: {node_data.shape}")
                elif hasattr(node_data, '__len__'):
                    print(f"   Length: {len(node_data)}")
                    print(f"   First few values: {node_data[:5]}")
                break
        
        if not found_node_key:
            print("❌ Could not find node information with standard keys")
            print("Available keys:", list(first_graph.keys()))
            
            # Look for any key that might contain atomic information
            for key in first_graph.keys():
                value = first_graph[key]
                if isinstance(value, (list, np.ndarray)) and len(value) > 0:
                    print(f"Potential key '{key}': {type(value)}, length={len(value)}")
                    if hasattr(value, 'dtype'):
                        print(f"  dtype: {value.dtype}")
                    print(f"  sample values: {value[:3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in simple loading: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Dataset Structure Debug Tool")
    print("="*50)
    
    examine_dataset_structure()
    test_simple_loading()
