{"model": {"hidden_channels": 512, "num_conv_layers": 4, "primary_caps": 16, "primary_dim": 32, "secondary_caps": 12, "secondary_dim": 32, "dropout_rate": 0.05}, "training": {"batch_size": 32, "learning_rate": 0.003, "weight_decay": 5e-06, "early_stopping_patience": 75, "grad_clip_norm": 0.3}, "scheduler": {"type": "ReduceLROnPlateau", "mode": "min", "factor": 0.3, "patience": 20, "min_lr": 5e-07, "verbose": true}, "data": {"train_split": 0.8, "val_split": 0.1, "test_split": 0.1, "random_seed": 42}, "conv_layer": {"num_rbf": 32, "cutoff": 12.0, "lmax": 2}, "capsule": {"routing_iterations": 3}}