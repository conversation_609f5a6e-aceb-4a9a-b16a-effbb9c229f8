# ALIGNN Baseline for CGN-E3 Comparison

This directory contains an ALIGNN (Atomistic Line Graph Neural Network) implementation designed for performance comparison with the CGN-E3 model. The ALIGNN implementation is compatible with the same dataset format used by CGN-E3, enabling fair and direct performance comparisons.

## Overview

ALIGNN is a graph neural network architecture specifically designed for crystal property prediction. It introduces the concept of line graphs to explicitly model three-body interactions in atomistic systems, complementing the two-body interactions captured by traditional atomic graphs.

### Key Features

- **Line Graph Architecture**: Models both two-body (atomic graph) and three-body (line graph) interactions
- **Edge-Gated Convolutions**: Uses edge features to gate message passing
- **Compatible Dataset Format**: Works with the same data format as CGN-E3
- **Comprehensive Evaluation**: Includes detailed performance metrics and visualization

## Architecture

The ALIGNN model consists of:

1. **Atomic Graph**: Nodes represent atoms, edges represent bonds
2. **Line Graph**: Nodes represent bonds, edges represent angles (three-body interactions)
3. **Dual Convolution**: Alternates between line graph and atomic graph updates
4. **Edge Gating**: Uses edge features to control information flow

## Installation

The ALIGNN baseline uses the same dependencies as CGN-E3:

```bash
# Install required packages (if not already installed)
pip install torch torch-geometric
pip install numpy pandas scikit-learn matplotlib seaborn
pip install tqdm
```

## Usage

### Basic Training

Train an ALIGNN model on the same dataset as CGN-E3:

```bash
python main_alignn.py \
    --dataset_path /path/to/dataset \
    --target_name e_form \
    --output_dir results/alignn_e_form \
    --epochs 200
```

### Configuration

Customize model and training parameters using a configuration file:

```bash
python main_alignn.py \
    --dataset_path /path/to/dataset \
    --target_name band_gap \
    --output_dir results/alignn_bandgap \
    --config config/alignn_config.json
```

### Model Comparison

Compare ALIGNN and CGN-E3 performance directly:

```bash
python examples/alignn_comparison.py \
    --dataset_path /path/to/dataset \
    --target_name e_form \
    --output_dir results/comparison \
    --epochs 200
```

## Configuration Options

The model can be configured through JSON files or command-line arguments:

### Model Parameters
- `hidden_channels`: Hidden layer dimensions (default: 128)
- `num_layers`: Number of ALIGNN convolution layers (default: 4)
- `dropout`: Dropout rate (default: 0.1)
- `pooling`: Global pooling method ('mean', 'max', 'add')
- `use_batch_norm`: Whether to use batch normalization

### Training Parameters
- `num_epochs`: Number of training epochs (default: 200)
- `batch_size`: Batch size (default: 32)
- `learning_rate`: Learning rate (default: 1e-3)
- `weight_decay`: Weight decay for regularization (default: 1e-5)
- `patience`: Early stopping patience (default: 20)

### Data Parameters
- `train_ratio`: Training set ratio (default: 0.8)
- `val_ratio`: Validation set ratio (default: 0.1)
- `test_ratio`: Test set ratio (default: 0.1)
- `cutoff`: Distance cutoff for line graph construction (default: 8.0)
- `add_self_loops`: Whether to add self-loops to line graph

## Dataset Format

The ALIGNN implementation uses the same dataset format as CGN-E3:

```
dataset/
├── BandgapTargets.npz          # Graph structures
├── BandgapTargets_config.json  # Atomic numbers and features
└── BandgapTargets.csv          # Target properties
```

## Output Files

Training produces the following outputs:

- `best_model.pt`: Trained model checkpoint
- `results.json`: Detailed results and metrics
- `predictions.json`: Test set predictions
- `predictions_plot.png`: Prediction visualization
- `performance_report.txt`: Performance analysis
- `training.log`: Training logs
- `config.json`: Configuration used

## Performance Metrics

The evaluation includes comprehensive metrics:

- **Mean Absolute Error (MAE)**
- **Root Mean Square Error (RMSE)**
- **R² Score**
- **Mean Absolute Percentage Error (MAPE)**
- **Error distribution percentiles**
- **Residual analysis**

## API Usage

You can also use the ALIGNN implementation programmatically:

```python
from alignn_baseline import ALIGNNDataset, ALIGNN
from alignn_baseline.utils import run_alignn_pipeline

# Load dataset
dataset = ALIGNNDataset(
    path="path/to/dataset",
    target_name="e_form",
    cutoff=8.0
)

# Create model
model = ALIGNN(
    node_features=dataset.n_node_feat,
    edge_features=4,  # Based on dataset
    hidden_channels=128,
    num_layers=4
)

# Run complete pipeline
results = run_alignn_pipeline(
    dataset_path="path/to/dataset",
    target_name="e_form",
    output_dir="results/alignn"
)
```

## Comparison with CGN-E3

### Architectural Differences

| Aspect | CGN-E3 | ALIGNN |
|--------|--------|--------|
| **Symmetry** | E(3)-equivariant | Permutation invariant |
| **Three-body** | Capsule networks | Line graphs |
| **Features** | Spherical harmonics | Edge-gated convolutions |
| **Complexity** | Higher | Lower |

### Expected Performance Trade-offs

- **CGN-E3**: Better symmetry preservation, potentially higher accuracy
- **ALIGNN**: Simpler architecture, faster training, good baseline performance

## Troubleshooting

### Common Issues

1. **Memory Issues**: Reduce batch size or number of layers
2. **Line Graph Construction**: Some crystals may have few/no line graph edges
3. **Convergence**: Adjust learning rate or use different optimizer

### Performance Tips

1. **Batch Size**: Use 32-64 for optimal performance
2. **Learning Rate**: Start with 1e-3, reduce if training is unstable
3. **Early Stopping**: Use patience=20 to avoid overfitting
4. **Normalization**: Target normalization is crucial for stable training

## References

1. [ALIGNN Paper](https://www.nature.com/articles/s41524-021-00650-1)
2. [ALIGNN GitHub](https://github.com/usnistgov/alignn)
3. [PyTorch Geometric](https://pytorch-geometric.readthedocs.io/)

## Citation

If you use this ALIGNN implementation, please cite both the original ALIGNN paper and this work:

```bibtex
@article{choudhary2021alignn,
  title={Atomistic line graph neural network for improved materials property predictions},
  author={Choudhary, Kamal and DeCost, Brian},
  journal={Nature Communications},
  volume={12},
  pages={6679},
  year={2021}
}
```
