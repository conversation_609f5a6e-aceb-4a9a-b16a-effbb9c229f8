"""
Complete pipeline for ALIGNN model training and evaluation.

This module provides end-to-end pipeline functions for training and evaluating
ALIGNN models on crystal property prediction tasks.
"""

import os
import json
import torch
import numpy as np
from torch.utils.data import DataLoader, random_split
import logging
from typing import Dict, Any, Tuple, Optional

from ..data import ALIGNNDataset
from ..models import ALIGNN
from .data_utils import collate_alignn_batch, compute_graph_statistics, normalize_targets
from .training import train_alignn, setup_logging
from .evaluation import evaluate_alignn, plot_predictions, analyze_model_performance


def run_alignn_pipeline(dataset_path: str, target_name: str, output_dir: str,
                       config: Dict[str, Any] = None, device: str = 'auto') -> Dict[str, Any]:
    """
    Run complete ALIGNN training and evaluation pipeline.
    
    Args:
        dataset_path: Path to dataset directory
        target_name: Name of target property to predict
        output_dir: Directory to save results
        config: Configuration dictionary
        device: Device to use ('auto', 'cpu', 'cuda')
        
    Returns:
        Dictionary containing results and metrics
    """
    # Setup default configuration
    default_config = {
        'model': {
            'hidden_channels': 128,
            'num_layers': 4,
            'dropout': 0.1,
            'pooling': 'mean',
            'use_batch_norm': True
        },
        'training': {
            'num_epochs': 200,
            'batch_size': 32,
            'learning_rate': 1e-3,
            'weight_decay': 1e-5,
            'patience': 20,
            'min_delta': 1e-4
        },
        'data': {
            'train_ratio': 0.8,
            'val_ratio': 0.1,
            'test_ratio': 0.1,
            'cutoff': 8.0,
            'add_self_loops': True
        }
    }
    
    if config:
        # Merge with provided config
        for section in config:
            if section in default_config:
                default_config[section].update(config[section])
            else:
                default_config[section] = config[section]
    
    config = default_config
    
    # Setup output directory and logging
    os.makedirs(output_dir, exist_ok=True)
    setup_logging(os.path.join(output_dir, 'training.log'))
    
    # Save configuration
    with open(os.path.join(output_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=2)
    
    # Setup device
    if device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device)
    
    logging.info(f"Using device: {device}")
    
    # Load dataset
    logging.info("Loading dataset...")
    dataset = ALIGNNDataset(
        path=dataset_path,
        target_name=target_name,
        cutoff=config['data']['cutoff'],
        add_self_loops=config['data']['add_self_loops']
    )
    
    logging.info(f"Dataset loaded: {len(dataset)} samples")
    
    # Get dataset statistics
    dataset_stats = dataset.get_statistics()
    logging.info(f"Dataset statistics: {dataset_stats}")
    
    # Compute graph statistics
    graph_stats = compute_graph_statistics(dataset)
    logging.info(f"Graph statistics: {graph_stats}")
    
    # Split dataset
    train_size = int(config['data']['train_ratio'] * len(dataset))
    val_size = int(config['data']['val_ratio'] * len(dataset))
    test_size = len(dataset) - train_size - val_size
    
    train_dataset, val_dataset, test_dataset = random_split(
        dataset, [train_size, val_size, test_size],
        generator=torch.Generator().manual_seed(42)
    )
    
    logging.info(f"Dataset split: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['training']['batch_size'],
        shuffle=True,
        collate_fn=collate_alignn_batch,
        num_workers=0  # Set to 0 to avoid multiprocessing issues
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['training']['batch_size'],
        shuffle=False,
        collate_fn=collate_alignn_batch,
        num_workers=0
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['training']['batch_size'],
        shuffle=False,
        collate_fn=collate_alignn_batch,
        num_workers=0
    )
    
    # Get sample to determine input dimensions
    sample = dataset[0]
    node_features = sample['graph'].x.size(1)
    edge_features = sample['graph'].edge_attr.size(1)
    
    logging.info(f"Input dimensions: nodes={node_features}, edges={edge_features}")
    
    # Create model
    model = ALIGNN(
        node_features=node_features,
        edge_features=edge_features,
        hidden_channels=config['model']['hidden_channels'],
        num_layers=config['model']['num_layers'],
        num_classes=1,
        dropout=config['model']['dropout'],
        pooling=config['model']['pooling'],
        use_batch_norm=config['model']['use_batch_norm']
    )
    
    logging.info(f"Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
    
    # Normalize targets
    all_targets = [dataset[i]['target'].item() for i in range(len(dataset))]
    target_mean = np.mean(all_targets)
    target_std = np.std(all_targets)
    
    logging.info(f"Target normalization: mean={target_mean:.4f}, std={target_std:.4f}")
    
    # Train model
    logging.info("Starting training...")
    history = train_alignn(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=config['training']['num_epochs'],
        learning_rate=config['training']['learning_rate'],
        weight_decay=config['training']['weight_decay'],
        device=device,
        save_path=os.path.join(output_dir, 'best_model.pt'),
        target_mean=target_mean,
        target_std=target_std,
        patience=config['training']['patience'],
        min_delta=config['training']['min_delta']
    )
    
    # Load best model for evaluation
    checkpoint = torch.load(os.path.join(output_dir, 'best_model.pt'), map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # Evaluate on test set
    logging.info("Evaluating on test set...")
    test_results = evaluate_alignn(
        model=model,
        data_loader=test_loader,
        device=device,
        target_mean=target_mean,
        target_std=target_std,
        return_predictions=True
    )
    
    # Generate performance report
    performance_report = analyze_model_performance(test_results, f"ALIGNN - {target_name}")
    logging.info(performance_report)
    
    # Save performance report
    with open(os.path.join(output_dir, 'performance_report.txt'), 'w') as f:
        f.write(performance_report)
    
    # Create prediction plots
    plot_predictions(
        targets=test_results['targets'],
        predictions=test_results['predictions'],
        title=f"ALIGNN Predictions - {target_name}",
        save_path=os.path.join(output_dir, 'predictions_plot.png'),
        show_plot=False
    )
    
    # Save detailed results
    results = {
        'config': config,
        'dataset_stats': dataset_stats,
        'graph_stats': graph_stats,
        'target_normalization': {
            'mean': target_mean,
            'std': target_std
        },
        'training_history': history,
        'test_metrics': {k: v for k, v in test_results.items() 
                        if k not in ['predictions', 'targets', 'material_ids']},
        'model_info': {
            'num_parameters': sum(p.numel() for p in model.parameters()),
            'node_features': node_features,
            'edge_features': edge_features
        }
    }
    
    # Save results as JSON
    with open(os.path.join(output_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    # Save predictions
    predictions_data = {
        'material_ids': test_results['material_ids'],
        'targets': test_results['targets'].tolist(),
        'predictions': test_results['predictions'].tolist()
    }
    
    with open(os.path.join(output_dir, 'predictions.json'), 'w') as f:
        json.dump(predictions_data, f, indent=2)
    
    logging.info(f"Pipeline completed. Results saved to {output_dir}")
    
    return results


def load_alignn_model(model_path: str, config_path: str = None, device: str = 'auto') -> Tuple[ALIGNN, Dict[str, Any]]:
    """
    Load a trained ALIGNN model.
    
    Args:
        model_path: Path to saved model checkpoint
        config_path: Path to model configuration (optional)
        device: Device to load model on
        
    Returns:
        Tuple of (model, checkpoint_info)
    """
    if device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device)
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location=device)
    
    # Load configuration if provided
    if config_path and os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        # Use default configuration
        config = {
            'model': {
                'hidden_channels': 128,
                'num_layers': 4,
                'dropout': 0.1,
                'pooling': 'mean',
                'use_batch_norm': True
            }
        }
    
    # Create model (need to determine input dimensions from checkpoint)
    # This is a limitation - we should save model architecture in checkpoint
    model = ALIGNN(
        node_features=128,  # Default - should be saved in checkpoint
        edge_features=128,  # Default - should be saved in checkpoint
        **config['model']
    )
    
    # Load model state
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    model.eval()
    
    return model, checkpoint
