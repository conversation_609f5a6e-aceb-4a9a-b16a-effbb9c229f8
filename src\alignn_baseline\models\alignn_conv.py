"""
ALIGNN convolution layers.

This module implements the core convolution layers for ALIGNN:
- EdgeGatedGraphConv: Edge-gated graph convolution
- ALIGNNConv: Combined line graph and atomic graph convolution
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import MessagePassing
from torch_geometric.utils import add_self_loops, degree, softmax
from torch_geometric.typing import Adj, OptTensor
from typing import Optional


class EdgeGatedGraphConv(MessagePassing):
    """
    Edge-gated graph convolution layer.
    
    This layer implements edge-gated message passing where edge features
    are used to gate the messages between nodes.
    
    Args:
        in_channels (int): Input node feature dimensions
        out_channels (int): Output node feature dimensions
        edge_dim (int): Edge feature dimensions
        dropout (float): Dropout rate
        bias (bool): Whether to use bias
    """
    
    def __init__(self, in_channels: int, out_channels: int, edge_dim: int,
                 dropout: float = 0.0, bias: bool = True):
        super().__init__(aggr='add', node_dim=0)
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.edge_dim = edge_dim
        self.dropout = dropout
        
        # Node transformation
        self.node_transform = nn.Linear(in_channels, out_channels, bias=bias)
        
        # Edge gating mechanism
        self.edge_gate = nn.Sequential(
            nn.Linear(edge_dim, out_channels),
            nn.Sigmoid()
        )
        
        # Message transformation
        self.message_transform = nn.Sequential(
            nn.Linear(in_channels + edge_dim, out_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # Self-loop transformation
        self.self_transform = nn.Linear(in_channels, out_channels, bias=bias)
        
        self.reset_parameters()
    
    def reset_parameters(self):
        """Reset layer parameters."""
        self.node_transform.reset_parameters()
        for layer in self.edge_gate:
            if hasattr(layer, 'reset_parameters'):
                layer.reset_parameters()
        for layer in self.message_transform:
            if hasattr(layer, 'reset_parameters'):
                layer.reset_parameters()
        self.self_transform.reset_parameters()
    
    def forward(self, x: torch.Tensor, edge_index: Adj, 
                edge_attr: OptTensor = None) -> torch.Tensor:
        """
        Forward pass of edge-gated graph convolution.
        
        Args:
            x: Node features [num_nodes, in_channels]
            edge_index: Edge connectivity [2, num_edges]
            edge_attr: Edge features [num_edges, edge_dim]
            
        Returns:
            Updated node features [num_nodes, out_channels]
        """
        if edge_attr is None:
            raise ValueError("Edge attributes are required for EdgeGatedGraphConv")
        
        # Add self-loops
        edge_index, edge_attr = add_self_loops(
            edge_index, edge_attr, fill_value=0.0, num_nodes=x.size(0)
        )
        
        # Propagate messages
        out = self.propagate(edge_index, x=x, edge_attr=edge_attr)
        
        # Add self-connection
        out = out + self.self_transform(x)
        
        return out
    
    def message(self, x_j: torch.Tensor, edge_attr: torch.Tensor) -> torch.Tensor:
        """
        Create messages from source nodes to target nodes.
        
        Args:
            x_j: Source node features [num_edges, in_channels]
            edge_attr: Edge features [num_edges, edge_dim]
            
        Returns:
            Messages [num_edges, out_channels]
        """
        # Combine node and edge features
        combined = torch.cat([x_j, edge_attr], dim=-1)
        
        # Transform to message
        message = self.message_transform(combined)
        
        # Apply edge gating
        gate = self.edge_gate(edge_attr)
        
        return message * gate


class ALIGNNConv(nn.Module):
    """
    ALIGNN convolution layer combining line graph and atomic graph convolutions.
    
    This is the core layer of ALIGNN that processes both the line graph
    (for three-body interactions) and the atomic graph (for two-body interactions).
    
    Args:
        node_features (int): Number of node features
        edge_features (int): Number of edge features
        hidden_channels (int): Hidden layer dimensions
        dropout (float): Dropout rate
    """
    
    def __init__(self, node_features: int, edge_features: int, 
                 hidden_channels: int, dropout: float = 0.0):
        super().__init__()
        
        self.node_features = node_features
        self.edge_features = edge_features
        self.hidden_channels = hidden_channels
        self.dropout = dropout
        
        # Line graph convolution (for three-body interactions)
        self.line_graph_conv = EdgeGatedGraphConv(
            in_channels=edge_features,
            out_channels=hidden_channels,
            edge_dim=4,  # Line graph edge features (angle info)
            dropout=dropout
        )
        
        # Atomic graph convolution (for two-body interactions)
        self.atomic_graph_conv = EdgeGatedGraphConv(
            in_channels=node_features,
            out_channels=hidden_channels,
            edge_dim=hidden_channels,  # Updated edge features from line graph
            dropout=dropout
        )
        
        # Edge feature update
        self.edge_update = nn.Sequential(
            nn.Linear(edge_features + hidden_channels, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # Node feature update
        self.node_update = nn.Sequential(
            nn.Linear(node_features + hidden_channels, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # Layer normalization
        self.node_norm = nn.LayerNorm(hidden_channels)
        self.edge_norm = nn.LayerNorm(hidden_channels)
    
    def forward(self, atomic_graph_data, line_graph_data):
        """
        Forward pass of ALIGNN convolution.
        
        Args:
            atomic_graph_data: Atomic graph data (nodes=atoms, edges=bonds)
            line_graph_data: Line graph data (nodes=bonds, edges=angles)
            
        Returns:
            tuple: Updated (atomic_graph_features, line_graph_features)
        """
        # Extract data
        x_atomic = atomic_graph_data.x
        edge_index_atomic = atomic_graph_data.edge_index
        edge_attr_atomic = atomic_graph_data.edge_attr
        
        x_line = line_graph_data.x
        edge_index_line = line_graph_data.edge_index
        edge_attr_line = line_graph_data.edge_attr
        
        # Step 1: Update line graph (three-body interactions)
        if edge_index_line.size(1) > 0:
            x_line_updated = self.line_graph_conv(x_line, edge_index_line, edge_attr_line)
            x_line_updated = self.edge_norm(x_line_updated)
        else:
            # Handle case with no line graph edges
            x_line_updated = torch.zeros(x_line.size(0), self.hidden_channels, 
                                       device=x_line.device, dtype=x_line.dtype)
        
        # Step 2: Update edge features in atomic graph using line graph information
        if x_line.size(0) > 0:
            # Combine original edge features with line graph updates
            edge_attr_combined = torch.cat([edge_attr_atomic, x_line_updated], dim=-1)
            edge_attr_updated = self.edge_update(edge_attr_combined)
        else:
            # Fallback if no line graph nodes
            edge_attr_updated = self.edge_update(
                torch.cat([edge_attr_atomic, 
                          torch.zeros(edge_attr_atomic.size(0), self.hidden_channels,
                                    device=edge_attr_atomic.device)], dim=-1)
            )
        
        edge_attr_updated = self.edge_norm(edge_attr_updated)
        
        # Step 3: Update atomic graph (two-body interactions)
        x_atomic_updated = self.atomic_graph_conv(
            x_atomic, edge_index_atomic, edge_attr_updated
        )
        x_atomic_updated = self.node_norm(x_atomic_updated)
        
        # Step 4: Update node features
        x_atomic_combined = torch.cat([x_atomic, x_atomic_updated], dim=-1)
        x_atomic_final = self.node_update(x_atomic_combined)
        
        # Create updated data objects
        from torch_geometric.data import Data
        
        updated_atomic_graph = Data(
            x=x_atomic_final,
            edge_index=edge_index_atomic,
            edge_attr=edge_attr_updated,
            pos=atomic_graph_data.pos if hasattr(atomic_graph_data, 'pos') else None,
            num_nodes=atomic_graph_data.num_nodes
        )
        
        updated_line_graph = Data(
            x=x_line_updated,
            edge_index=edge_index_line,
            edge_attr=edge_attr_line,
            num_nodes=line_graph_data.num_nodes
        )
        
        return updated_atomic_graph, updated_line_graph
