"""
Tests for utility functions.
"""

import pytest
import torch
import torch.nn as nn
import tempfile
import os
import pandas as pd
from torch.utils.data import DataLoader
from torch_geometric.data import Data, Batch

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from cgn_e3.utils.evaluation import evaluate_spatial_gnn, calculate_additional_metrics
from cgn_e3.models import E3EquivariantCrystalGNNCapsNet


class DummyModel(nn.Module):
    """Dummy model for testing."""
    
    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(10, 1)
    
    def forward(self, data):
        # Simple model that just uses node features
        batch_size = data.batch.max().item() + 1 if hasattr(data, 'batch') else 1
        
        # Average node features per graph
        if hasattr(data, 'batch'):
            output = torch.zeros(batch_size, 1, device=data.x.device)
            for i in range(batch_size):
                mask = data.batch == i
                if mask.sum() > 0:
                    avg_features = data.x[mask].mean(dim=0)
                    output[i] = self.linear(avg_features)
        else:
            avg_features = data.x.mean(dim=0)
            output = self.linear(avg_features).unsqueeze(0)
        
        return output


class TestEvaluationUtils:
    """Test cases for evaluation utilities."""
    
    def create_test_data(self):
        """Create test data for evaluation."""
        data_list = []
        for i in range(10):
            num_nodes = 5 + (i % 3)
            data = Data(
                x=torch.randn(num_nodes, 10),
                edge_index=torch.randint(0, num_nodes, (2, num_nodes)),
                edge_attr=torch.randn(num_nodes, 2),
                pos=torch.randn(num_nodes, 3),
                y=torch.randn(1, 1),
                material_id=f"test_material_{i}"
            )
            data_list.append(data)
        
        return DataLoader(data_list, batch_size=3, collate_fn=Batch.from_data_list)
    
    def test_evaluate_spatial_gnn(self):
        """Test model evaluation function."""
        model = DummyModel()
        model.eval()
        test_loader = self.create_test_data()
        device = torch.device('cpu')
        
        results = evaluate_spatial_gnn(model, test_loader, device)
        
        # Check that all required metrics are present
        assert 'mse' in results
        assert 'rmse' in results
        assert 'mae' in results
        assert 'r2' in results
        assert 'predictions' in results
        assert 'targets' in results
        assert 'material_ids' in results
        
        # Check that metrics are reasonable
        assert results['mse'] >= 0
        assert results['rmse'] >= 0
        assert results['mae'] >= 0
        assert len(results['predictions']) == 10
        assert len(results['targets']) == 10
        assert len(results['material_ids']) == 10
    
    def test_evaluate_with_results_path(self):
        """Test evaluation with results saving."""
        model = DummyModel()
        model.eval()
        test_loader = self.create_test_data()
        device = torch.device('cpu')
        
        with tempfile.TemporaryDirectory() as temp_dir:
            results_path = os.path.join(temp_dir, "test_results.csv")
            
            results = evaluate_spatial_gnn(model, test_loader, device, results_path)
            
            # Check that file was created
            assert os.path.exists(results_path)
            
            # Check file contents
            df = pd.read_csv(results_path)
            assert 'mse' in df.columns
            assert 'rmse' in df.columns
            assert 'mae' in df.columns
            assert 'r2' in df.columns
            assert len(df) == 1  # Should have one row of metrics
    
    def test_calculate_additional_metrics(self):
        """Test additional metrics calculation."""
        predictions = [1.0, 2.0, 3.0, 4.0, 5.0]
        targets = [1.1, 1.9, 3.2, 3.8, 5.1]
        
        metrics = calculate_additional_metrics(predictions, targets)
        
        assert 'mape' in metrics
        assert 'max_error' in metrics
        assert 'median_ae' in metrics
        assert 'explained_variance' in metrics
        
        # Check that metrics are reasonable
        assert metrics['mape'] >= 0
        assert metrics['max_error'] >= 0
        assert metrics['median_ae'] >= 0
        assert -1 <= metrics['explained_variance'] <= 1


class TestModelIntegration:
    """Integration tests for the complete model."""
    
    def create_minimal_data(self):
        """Create minimal data for integration testing."""
        data_list = []
        for i in range(3):
            num_nodes = 3
            data = Data(
                x=torch.randn(num_nodes, 5),
                edge_index=torch.tensor([[0, 1, 2], [1, 2, 0]], dtype=torch.long),
                edge_attr=torch.randn(3, 2),
                pos=torch.randn(num_nodes, 3),
                y=torch.randn(1, 1)
            )
            data_list.append(data)
        
        return DataLoader(data_list, batch_size=2, collate_fn=Batch.from_data_list)
    
    def test_model_forward_backward(self):
        """Test that model can perform forward and backward passes."""
        model = E3EquivariantCrystalGNNCapsNet(
            node_features=5,
            edge_features=2,
            hidden_channels=16,
            num_conv_layers=1,
            primary_caps=2,
            primary_dim=4,
            secondary_caps=2,
            secondary_dim=4,
            dropout_rate=0.0
        )
        
        data_loader = self.create_minimal_data()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
        criterion = nn.MSELoss()
        
        model.train()
        for batch in data_loader:
            optimizer.zero_grad()
            
            output = model(batch)
            loss = criterion(output, batch.y)
            
            # Check that loss is computed
            assert loss.item() >= 0
            
            # Check that gradients can be computed
            loss.backward()
            
            # Check that some gradients are non-zero
            has_grad = False
            for param in model.parameters():
                if param.grad is not None and torch.any(param.grad != 0):
                    has_grad = True
                    break
            assert has_grad, "No gradients computed"
            
            optimizer.step()
            break  # Just test one batch
    
    def test_model_evaluation_mode(self):
        """Test model in evaluation mode."""
        model = E3EquivariantCrystalGNNCapsNet(
            node_features=5,
            edge_features=2,
            hidden_channels=16,
            num_conv_layers=1,
            primary_caps=2,
            primary_dim=4,
            secondary_caps=2,
            secondary_dim=4
        )
        
        data_loader = self.create_minimal_data()
        
        model.eval()
        with torch.no_grad():
            for batch in data_loader:
                output = model(batch)
                
                # Check output shape
                expected_batch_size = batch.batch.max().item() + 1
                assert output.shape == (expected_batch_size, 1)
                
                # Check that output is finite
                assert torch.all(torch.isfinite(output))
                break


if __name__ == "__main__":
    pytest.main([__file__])
