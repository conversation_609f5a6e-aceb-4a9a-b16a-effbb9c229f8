#!/usr/bin/env python3
"""
Test suite for ALIGNN baseline implementation.

This module contains comprehensive tests for the ALIGNN baseline model
to ensure compatibility with CGN-E3 dataset format and correct functionality.
"""

import pytest
import torch
import numpy as np
import tempfile
import os
import json
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from alignn_baseline.data import ALIGNNDataset, create_line_graph
from alignn_baseline.models import ALIGNN, ALIGNNConv, EdgeGatedGraphConv
from alignn_baseline.utils import collate_alignn_batch, train_alignn, evaluate_alignn
from torch_geometric.data import Data


class TestLineGraphConstruction:
    """Test line graph construction functionality."""
    
    def test_create_line_graph_simple(self):
        """Test line graph creation with a simple graph."""
        # Create a simple triangle graph
        x = torch.randn(3, 4)  # 3 nodes, 4 features
        edge_index = torch.tensor([[0, 1, 1, 2, 2, 0], [1, 0, 2, 1, 0, 2]], dtype=torch.long)
        edge_attr = torch.randn(6, 2)  # 6 edges, 2 features
        pos = torch.tensor([[0.0, 0.0, 0.0], [1.0, 0.0, 0.0], [0.5, 1.0, 0.0]])
        
        data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr, pos=pos)
        
        # Create line graph
        atomic_graph, line_graph = create_line_graph(data)
        
        # Check that atomic graph is unchanged
        assert torch.equal(atomic_graph.x, x)
        assert torch.equal(atomic_graph.edge_index, edge_index)
        
        # Check line graph properties
        assert line_graph.num_nodes == 6  # Number of edges in original graph
        assert line_graph.x.shape[0] == 6
        assert line_graph.edge_index.shape[0] == 2
    
    def test_create_line_graph_empty(self):
        """Test line graph creation with empty graph."""
        x = torch.randn(2, 4)
        edge_index = torch.empty(2, 0, dtype=torch.long)
        edge_attr = torch.empty(0, 2)
        pos = torch.randn(2, 3)
        
        data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr, pos=pos)
        
        atomic_graph, line_graph = create_line_graph(data)
        
        assert line_graph.num_nodes == 0
        assert line_graph.edge_index.shape[1] == 0


class TestALIGNNConvolution:
    """Test ALIGNN convolution layers."""
    
    def test_edge_gated_conv(self):
        """Test EdgeGatedGraphConv layer."""
        in_channels = 8
        out_channels = 16
        edge_dim = 4
        
        conv = EdgeGatedGraphConv(in_channels, out_channels, edge_dim)
        
        # Create test data
        x = torch.randn(10, in_channels)
        edge_index = torch.tensor([[0, 1, 2, 3], [1, 2, 3, 0]], dtype=torch.long)
        edge_attr = torch.randn(4, edge_dim)
        
        # Forward pass
        out = conv(x, edge_index, edge_attr)
        
        assert out.shape == (10, out_channels)
        assert not torch.isnan(out).any()
    
    def test_alignn_conv(self):
        """Test ALIGNNConv layer."""
        node_features = 8
        edge_features = 4
        hidden_channels = 16
        
        conv = ALIGNNConv(node_features, edge_features, hidden_channels)
        
        # Create atomic graph
        atomic_x = torch.randn(5, node_features)
        atomic_edge_index = torch.tensor([[0, 1, 2], [1, 2, 0]], dtype=torch.long)
        atomic_edge_attr = torch.randn(3, edge_features)
        atomic_pos = torch.randn(5, 3)
        
        atomic_data = Data(
            x=atomic_x,
            edge_index=atomic_edge_index,
            edge_attr=atomic_edge_attr,
            pos=atomic_pos,
            num_nodes=5
        )
        
        # Create line graph
        line_x = torch.randn(3, edge_features)
        line_edge_index = torch.tensor([[0, 1], [1, 0]], dtype=torch.long)
        line_edge_attr = torch.randn(2, 4)
        
        line_data = Data(
            x=line_x,
            edge_index=line_edge_index,
            edge_attr=line_edge_attr,
            num_nodes=3
        )
        
        # Forward pass
        updated_atomic, updated_line = conv(atomic_data, line_data)
        
        assert updated_atomic.x.shape == (5, hidden_channels)
        assert updated_line.x.shape == (3, hidden_channels)


class TestALIGNNModel:
    """Test complete ALIGNN model."""
    
    def test_model_creation(self):
        """Test ALIGNN model creation."""
        model = ALIGNN(
            node_features=10,
            edge_features=4,
            hidden_channels=32,
            num_layers=2,
            num_classes=1
        )
        
        assert isinstance(model, ALIGNN)
        assert len(model.alignn_layers) == 2
    
    def test_model_forward(self):
        """Test ALIGNN model forward pass."""
        model = ALIGNN(
            node_features=8,
            edge_features=4,
            hidden_channels=16,
            num_layers=2,
            num_classes=1
        )
        
        # Create batch data
        atomic_x = torch.randn(10, 8)
        atomic_edge_index = torch.tensor([[0, 1, 2, 3, 4], [1, 2, 3, 4, 0]], dtype=torch.long)
        atomic_edge_attr = torch.randn(5, 4)
        atomic_pos = torch.randn(10, 3)
        batch = torch.zeros(10, dtype=torch.long)
        
        atomic_graph = Data(
            x=atomic_x,
            edge_index=atomic_edge_index,
            edge_attr=atomic_edge_attr,
            pos=atomic_pos,
            num_nodes=10
        )
        
        line_x = torch.randn(5, 4)
        line_edge_index = torch.tensor([[0, 1], [1, 0]], dtype=torch.long)
        line_edge_attr = torch.randn(2, 4)
        
        line_graph = Data(
            x=line_x,
            edge_index=line_edge_index,
            edge_attr=line_edge_attr,
            num_nodes=5
        )
        
        batch_data = {
            'graph': atomic_graph,
            'line_graph': line_graph,
            'batch': batch
        }
        
        # Forward pass
        output = model(batch_data)
        
        assert output.shape == (1, 1)  # 1 graph, 1 output
        assert not torch.isnan(output).any()


class TestDataUtils:
    """Test data utility functions."""
    
    def test_collate_function(self):
        """Test batch collation function."""
        # Create sample data
        samples = []
        for i in range(3):
            atomic_graph = Data(
                x=torch.randn(5, 8),
                edge_index=torch.tensor([[0, 1, 2], [1, 2, 0]], dtype=torch.long),
                edge_attr=torch.randn(3, 4),
                pos=torch.randn(5, 3),
                num_nodes=5
            )
            
            line_graph = Data(
                x=torch.randn(3, 4),
                edge_index=torch.tensor([[0, 1], [1, 0]], dtype=torch.long),
                edge_attr=torch.randn(2, 4),
                num_nodes=3
            )
            
            sample = {
                'graph': atomic_graph,
                'line_graph': line_graph,
                'target': torch.tensor([float(i)]),
                'material_id': f'material_{i}'
            }
            samples.append(sample)
        
        # Collate batch
        batch = collate_alignn_batch(samples)
        
        assert 'graph' in batch
        assert 'line_graph' in batch
        assert 'target' in batch
        assert 'material_ids' in batch
        assert batch['target'].shape == (3, 1)
        assert len(batch['material_ids']) == 3


class TestDataset:
    """Test ALIGNN dataset functionality."""
    
    def create_mock_dataset(self, temp_dir):
        """Create a mock dataset for testing."""
        # Create mock graph data
        graph_dict = {}
        for i in range(5):
            graph_dict[f'material_{i}'] = {
                'node_features': [1, 6, 8],  # H, C, O
                'cart_coords': [[0.0, 0.0, 0.0], [1.0, 0.0, 0.0], [0.0, 1.0, 0.0]],
                'neighbors': [[1, 2], [0, 2], [0, 1]],
                'bond_lengths': [[1.0, 1.4], [1.0, 1.4], [1.4, 1.4]],
                'type_counts': [2, 2, 2],
                'neighbor_counts': [[1, 1], [1, 1], [1, 1]]
            }
        
        # Save NPZ file
        np.savez(
            os.path.join(temp_dir, 'BandgapTargets.npz'),
            graph_dict=graph_dict
        )
        
        # Create config
        config = {
            'atomic_numbers': [1, 6, 8],
            'node_vectors': [[1, 0, 0], [0, 1, 0], [0, 0, 1]],
            'pos_dim': 3
        }
        
        with open(os.path.join(temp_dir, 'BandgapTargets_config.json'), 'w') as f:
            json.dump(config, f)
        
        # Create CSV
        import pandas as pd
        df = pd.DataFrame({
            'mpid': [f'material_{i}' for i in range(5)],
            'e_form': [0.1 * i for i in range(5)],
            'band_gap': [1.0 + 0.1 * i for i in range(5)]
        })
        df.to_csv(os.path.join(temp_dir, 'BandgapTargets.csv'), index=False)
    
    def test_dataset_loading(self):
        """Test dataset loading functionality."""
        with tempfile.TemporaryDirectory() as temp_dir:
            self.create_mock_dataset(temp_dir)
            
            # Load dataset
            dataset = ALIGNNDataset(
                path=temp_dir,
                target_name='e_form'
            )
            
            assert len(dataset) == 5
            
            # Test sample retrieval
            sample = dataset[0]
            assert 'graph' in sample
            assert 'line_graph' in sample
            assert 'target' in sample
            assert 'material_id' in sample
            
            # Check data types
            assert isinstance(sample['graph'], Data)
            assert isinstance(sample['line_graph'], Data)
            assert isinstance(sample['target'], torch.Tensor)


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
