{"model": {"hidden_channels": 128, "num_conv_layers": 1, "primary_caps": 4, "primary_dim": 8, "secondary_caps": 3, "secondary_dim": 8, "dropout_rate": 0.1}, "training": {"batch_size": 32, "learning_rate": 0.001, "weight_decay": 0.0001, "early_stopping_patience": 30, "grad_clip_norm": 1.0}, "scheduler": {"type": "ReduceLROnPlateau", "mode": "min", "factor": 0.7, "patience": 10, "min_lr": 1e-05, "verbose": true}, "data": {"train_split": 0.8, "val_split": 0.1, "test_split": 0.1, "random_seed": 42}, "conv_layer": {"num_rbf": 8, "cutoff": 8.0, "lmax": 1}, "capsule": {"routing_iterations": 1}}