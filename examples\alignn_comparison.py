#!/usr/bin/env python3
"""
Example script for comparing ALIGNN and CGN-E3 models.

This script demonstrates how to train both models on the same dataset
and compare their performance.
"""

import os
import sys
import json
import subprocess
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


def run_cgn_e3_training(dataset_path, target_name, output_dir, epochs=200):
    """Run CGN-E3 training."""
    print("Training CGN-E3 model...")
    
    cmd = [
        sys.executable, "main.py",
        "--dataset_path", dataset_path,
        "--target_name", target_name,
        "--epochs", str(epochs),
        "--output_dir", output_dir
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"CGN-E3 training failed: {result.stderr}")
        return None
    
    # Load results
    results_path = os.path.join(output_dir, "results.json")
    if os.path.exists(results_path):
        with open(results_path, 'r') as f:
            return json.load(f)
    
    return None


def run_alignn_training(dataset_path, target_name, output_dir, epochs=200):
    """Run ALIGNN training."""
    print("Training ALIGNN model...")
    
    cmd = [
        sys.executable, "main_alignn.py",
        "--dataset_path", dataset_path,
        "--target_name", target_name,
        "--epochs", str(epochs),
        "--output_dir", output_dir
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"ALIGNN training failed: {result.stderr}")
        return None
    
    # Load results
    results_path = os.path.join(output_dir, "results.json")
    if os.path.exists(results_path):
        with open(results_path, 'r') as f:
            return json.load(f)
    
    return None


def compare_results(cgn_e3_results, alignn_results, output_dir):
    """Compare and visualize results from both models."""
    
    # Extract test metrics
    cgn_e3_metrics = cgn_e3_results.get('test_metrics', {})
    alignn_metrics = alignn_results.get('test_metrics', {})
    
    # Create comparison report
    comparison = {
        'CGN-E3': {
            'MAE': cgn_e3_metrics.get('mae', 0),
            'RMSE': cgn_e3_metrics.get('rmse', 0),
            'R²': cgn_e3_metrics.get('r2', 0),
            'MAPE': cgn_e3_metrics.get('mape', 0),
            'Parameters': cgn_e3_results.get('model_info', {}).get('num_parameters', 0)
        },
        'ALIGNN': {
            'MAE': alignn_metrics.get('mae', 0),
            'RMSE': alignn_metrics.get('rmse', 0),
            'R²': alignn_metrics.get('r2', 0),
            'MAPE': alignn_metrics.get('mape', 0),
            'Parameters': alignn_results.get('model_info', {}).get('num_parameters', 0)
        }
    }
    
    # Generate comparison report
    report = """
Model Performance Comparison: CGN-E3 vs ALIGNN
==============================================

Metric Comparison:
                CGN-E3      ALIGNN      Winner
                ------      ------      ------
"""
    
    metrics = ['MAE', 'RMSE', 'R²', 'MAPE']
    for metric in metrics:
        cgn_val = comparison['CGN-E3'][metric]
        alignn_val = comparison['ALIGNN'][metric]
        
        if metric == 'R²':
            winner = 'CGN-E3' if cgn_val > alignn_val else 'ALIGNN'
        else:
            winner = 'CGN-E3' if cgn_val < alignn_val else 'ALIGNN'
        
        report += f"{metric:12s}    {cgn_val:8.4f}    {alignn_val:8.4f}    {winner}\n"
    
    # Model complexity comparison
    cgn_params = comparison['CGN-E3']['Parameters']
    alignn_params = comparison['ALIGNN']['Parameters']
    
    report += f"\nModel Complexity:\n"
    report += f"CGN-E3 Parameters:  {cgn_params:,}\n"
    report += f"ALIGNN Parameters:  {alignn_params:,}\n"
    report += f"Parameter Ratio:    {cgn_params/alignn_params:.2f}x\n"
    
    # Performance per parameter
    cgn_efficiency = comparison['CGN-E3']['R²'] / (cgn_params / 1e6)
    alignn_efficiency = comparison['ALIGNN']['R²'] / (alignn_params / 1e6)
    
    report += f"\nEfficiency (R² per Million Parameters):\n"
    report += f"CGN-E3:  {cgn_efficiency:.4f}\n"
    report += f"ALIGNN:  {alignn_efficiency:.4f}\n"
    
    # Overall assessment
    cgn_wins = sum(1 for metric in metrics if 
                   (metric == 'R²' and comparison['CGN-E3'][metric] > comparison['ALIGNN'][metric]) or
                   (metric != 'R²' and comparison['CGN-E3'][metric] < comparison['ALIGNN'][metric]))
    
    report += f"\nOverall Assessment:\n"
    report += f"CGN-E3 wins in {cgn_wins}/{len(metrics)} metrics\n"
    report += f"ALIGNN wins in {len(metrics)-cgn_wins}/{len(metrics)} metrics\n"
    
    if cgn_wins > len(metrics) // 2:
        report += "Winner: CGN-E3\n"
    elif cgn_wins < len(metrics) // 2:
        report += "Winner: ALIGNN\n"
    else:
        report += "Result: Tie\n"
    
    print(report)
    
    # Save comparison results
    comparison_data = {
        'comparison_metrics': comparison,
        'report': report,
        'cgn_e3_results': cgn_e3_results,
        'alignn_results': alignn_results
    }
    
    with open(os.path.join(output_dir, 'model_comparison.json'), 'w') as f:
        json.dump(comparison_data, f, indent=2)
    
    with open(os.path.join(output_dir, 'comparison_report.txt'), 'w') as f:
        f.write(report)
    
    return comparison_data


def main():
    """Main comparison script."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Compare CGN-E3 and ALIGNN models")
    parser.add_argument("--dataset_path", required=True, help="Path to dataset")
    parser.add_argument("--target_name", required=True, help="Target property name")
    parser.add_argument("--output_dir", required=True, help="Output directory")
    parser.add_argument("--epochs", type=int, default=200, help="Number of epochs")
    
    args = parser.parse_args()
    
    # Create output directories
    cgn_e3_dir = os.path.join(args.output_dir, "cgn_e3")
    alignn_dir = os.path.join(args.output_dir, "alignn")
    os.makedirs(cgn_e3_dir, exist_ok=True)
    os.makedirs(alignn_dir, exist_ok=True)
    
    print(f"Starting model comparison for target: {args.target_name}")
    print(f"Dataset: {args.dataset_path}")
    print(f"Output: {args.output_dir}")
    print(f"Epochs: {args.epochs}")
    print()
    
    # Train both models
    cgn_e3_results = run_cgn_e3_training(
        args.dataset_path, args.target_name, cgn_e3_dir, args.epochs
    )
    
    alignn_results = run_alignn_training(
        args.dataset_path, args.target_name, alignn_dir, args.epochs
    )
    
    # Compare results
    if cgn_e3_results and alignn_results:
        print("\nGenerating comparison report...")
        comparison_data = compare_results(cgn_e3_results, alignn_results, args.output_dir)
        print(f"\nComparison complete! Results saved to {args.output_dir}")
    else:
        print("Error: One or both models failed to train")
        if not cgn_e3_results:
            print("CGN-E3 training failed")
        if not alignn_results:
            print("ALIGNN training failed")


if __name__ == "__main__":
    main()
