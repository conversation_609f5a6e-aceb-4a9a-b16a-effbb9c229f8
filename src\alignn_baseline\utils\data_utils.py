"""
Data utilities for ALIGNN model training.

This module provides utilities for batching and processing ALIGNN data.
"""

import torch
from torch_geometric.data import Batch
from typing import List, Dict, Any


def collate_alignn_batch(batch: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Collate function for ALIGNN data loader.
    
    This function batches multiple graph samples into a single batch
    suitable for ALIGNN model training.
    
    Args:
        batch: List of sample dictionaries from ALIGNNDataset
        
    Returns:
        Batched data dictionary
    """
    # Separate atomic graphs and line graphs
    atomic_graphs = [sample['graph'] for sample in batch]
    line_graphs = [sample['line_graph'] for sample in batch]
    targets = [sample['target'] for sample in batch]
    material_ids = [sample['material_id'] for sample in batch]
    
    # Batch atomic graphs
    batched_atomic = Batch.from_data_list(atomic_graphs)
    
    # Batch line graphs (more complex due to variable sizes)
    batched_line = batch_line_graphs(line_graphs)
    
    # Stack targets
    batched_targets = torch.stack(targets, dim=0)
    
    return {
        'graph': batched_atomic,
        'line_graph': batched_line,
        'target': batched_targets,
        'material_ids': material_ids,
        'batch': batched_atomic.batch
    }


def batch_line_graphs(line_graphs: List) -> Batch:
    """
    Batch line graphs with special handling for empty graphs.
    
    Args:
        line_graphs: List of line graph Data objects
        
    Returns:
        Batched line graph
    """
    # Filter out empty line graphs and keep track of indices
    non_empty_graphs = []
    empty_indices = []
    
    for i, graph in enumerate(line_graphs):
        if graph.num_nodes > 0:
            non_empty_graphs.append(graph)
        else:
            empty_indices.append(i)
    
    if non_empty_graphs:
        # Batch non-empty graphs
        batched = Batch.from_data_list(non_empty_graphs)
        
        # If there were empty graphs, we need to handle them
        if empty_indices:
            # Create a mapping from original indices to batched indices
            batch_mapping = torch.zeros(len(line_graphs), dtype=torch.long)
            non_empty_idx = 0
            for i in range(len(line_graphs)):
                if i not in empty_indices:
                    batch_mapping[i] = non_empty_idx
                    non_empty_idx += 1
                else:
                    batch_mapping[i] = -1  # Mark empty graphs
        
        return batched
    else:
        # All graphs are empty, create an empty batch
        from torch_geometric.data import Data
        empty_graph = Data(
            x=torch.empty(0, 4),  # Assuming 4 edge features
            edge_index=torch.empty(2, 0, dtype=torch.long),
            edge_attr=torch.empty(0, 4),
            num_nodes=0
        )
        return Batch.from_data_list([empty_graph])


def prepare_alignn_input(sample: Dict[str, Any], device: torch.device) -> Dict[str, Any]:
    """
    Prepare a single sample for ALIGNN model input.
    
    Args:
        sample: Sample dictionary from dataset
        device: Target device
        
    Returns:
        Prepared input dictionary
    """
    # Move data to device
    graph = sample['graph'].to(device)
    line_graph = sample['line_graph'].to(device)
    target = sample['target'].to(device)
    
    # Create batch indices for single sample
    batch = torch.zeros(graph.num_nodes, dtype=torch.long, device=device)
    
    return {
        'graph': graph,
        'line_graph': line_graph,
        'target': target,
        'batch': batch,
        'material_id': sample['material_id']
    }


def compute_graph_statistics(dataset) -> Dict[str, float]:
    """
    Compute statistics about the graphs in the dataset.
    
    Args:
        dataset: ALIGNN dataset
        
    Returns:
        Dictionary of statistics
    """
    num_atoms_list = []
    num_bonds_list = []
    num_line_nodes_list = []
    num_line_edges_list = []
    
    for i in range(min(1000, len(dataset))):  # Sample first 1000 for efficiency
        sample = dataset[i]
        
        num_atoms_list.append(sample['graph'].num_nodes)
        num_bonds_list.append(sample['graph'].edge_index.size(1))
        num_line_nodes_list.append(sample['line_graph'].num_nodes)
        num_line_edges_list.append(sample['line_graph'].edge_index.size(1))
    
    stats = {
        'avg_num_atoms': float(torch.tensor(num_atoms_list, dtype=torch.float).mean()),
        'max_num_atoms': max(num_atoms_list),
        'min_num_atoms': min(num_atoms_list),
        'avg_num_bonds': float(torch.tensor(num_bonds_list, dtype=torch.float).mean()),
        'max_num_bonds': max(num_bonds_list),
        'min_num_bonds': min(num_bonds_list),
        'avg_num_line_nodes': float(torch.tensor(num_line_nodes_list, dtype=torch.float).mean()),
        'max_num_line_nodes': max(num_line_nodes_list),
        'min_num_line_nodes': min(num_line_nodes_list),
        'avg_num_line_edges': float(torch.tensor(num_line_edges_list, dtype=torch.float).mean()),
        'max_num_line_edges': max(num_line_edges_list),
        'min_num_line_edges': min(num_line_edges_list),
    }
    
    return stats


def normalize_targets(targets: torch.Tensor, mean: float = None, std: float = None):
    """
    Normalize target values.
    
    Args:
        targets: Target tensor
        mean: Mean for normalization (computed if None)
        std: Standard deviation for normalization (computed if None)
        
    Returns:
        tuple: (normalized_targets, mean, std)
    """
    if mean is None:
        mean = targets.mean().item()
    if std is None:
        std = targets.std().item()
    
    normalized = (targets - mean) / (std + 1e-8)
    
    return normalized, mean, std


def denormalize_targets(normalized_targets: torch.Tensor, mean: float, std: float):
    """
    Denormalize target values.
    
    Args:
        normalized_targets: Normalized target tensor
        mean: Mean used for normalization
        std: Standard deviation used for normalization
        
    Returns:
        Denormalized targets
    """
    return normalized_targets * std + mean
