#!/usr/bin/env python3
"""
Test script for the cleaned fullALIGNN.py implementation.

This script demonstrates how to use the complete ALIGNN training pipeline
with epoch tracking and loss display.
"""

import os
import sys
import torch
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src" / "alignn_baseline"))

def test_alignn_import():
    """Test if we can import the ALIGNN components."""
    try:
        from fullALIGNN import (
            ALIGNNDataset,
            ALIGNN,
            create_line_graph,
            train_alignn_with_epochs,
            evaluate_alignn,
            run_complete_alignn_pipeline
        )
        print("✅ Successfully imported all ALIGNN components")
        return True
    except ImportError as e:
        print(f"❌ Failed to import ALIGNN components: {e}")
        return False

def test_model_creation():
    """Test creating an ALIGNN model."""
    try:
        from fullALIGNN import ALIGNN
        
        # Create a simple model
        model = ALIGNN(
            node_features=64,
            edge_features=1,
            hidden_channels=32,
            num_layers=2,
            num_classes=1,
            dropout=0.1
        )
        
        num_params = sum(p.numel() for p in model.parameters())
        print(f"✅ Successfully created ALIGNN model with {num_params:,} parameters")
        return True
    except Exception as e:
        print(f"❌ Failed to create ALIGNN model: {e}")
        return False

def test_line_graph_creation():
    """Test line graph creation functionality."""
    try:
        from fullALIGNN import create_line_graph
        from torch_geometric.data import Data
        import torch
        
        # Create a simple test graph
        x = torch.randn(4, 3)  # 4 nodes, 3 features each
        edge_index = torch.tensor([[0, 1, 1, 2, 2, 3], [1, 0, 2, 1, 3, 2]], dtype=torch.long)
        edge_attr = torch.randn(6, 1)  # 6 edges, 1 feature each
        pos = torch.randn(4, 3)  # 3D positions
        
        data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr, pos=pos)
        
        # Create line graph
        atomic_graph, line_graph = create_line_graph(data)
        
        print(f"✅ Successfully created line graph:")
        print(f"   Original graph: {atomic_graph.num_nodes} nodes, {atomic_graph.edge_index.size(1)} edges")
        print(f"   Line graph: {line_graph.num_nodes} nodes, {line_graph.edge_index.size(1)} edges")
        return True
    except Exception as e:
        print(f"❌ Failed to create line graph: {e}")
        return False

def show_usage_example():
    """Show example usage of the complete pipeline."""
    print("\n" + "="*60)
    print("USAGE EXAMPLE")
    print("="*60)
    print("""
To use the complete ALIGNN training pipeline:

1. Command Line Usage:
   python src/alignn_baseline/fullALIGNN.py \\
       --dataset_path /path/to/dataset \\
       --target_name e_form \\
       --output_dir alignn_results \\
       --num_epochs 100 \\
       --batch_size 32 \\
       --learning_rate 0.001 \\
       --hidden_channels 128 \\
       --num_layers 4

2. Python API Usage:
   from src.alignn_baseline.fullALIGNN import run_complete_alignn_pipeline
   
   results = run_complete_alignn_pipeline(
       dataset_path="/path/to/dataset",
       target_name="e_form",
       output_dir="alignn_results",
       num_epochs=100,
       batch_size=32,
       learning_rate=0.001,
       hidden_channels=128,
       num_layers=4
   )

3. Key Features:
   ✅ Complete training pipeline with epoch tracking
   ✅ Detailed loss display per epoch
   ✅ Training and validation metrics (MAE, RMSE, R²)
   ✅ Early stopping with patience
   ✅ Learning rate scheduling
   ✅ Model checkpointing
   ✅ Training history visualization
   ✅ Comprehensive evaluation metrics
   ✅ Compatible with CGN-E3 dataset format

4. Output:
   - best_model.pt: Best model checkpoint
   - training_history.png: Training curves visualization
   - results.json: Complete training results and metrics
   - training.log: Detailed training logs

The implementation includes:
- ALIGNNDataset: Dataset loader compatible with CGN-E3 format
- ALIGNN: Complete model architecture with line graph processing
- EdgeGatedGraphConv: Edge-gated graph convolution layers
- ALIGNNConv: ALIGNN-specific convolution combining atomic and line graphs
- Complete training utilities with epoch tracking
- Evaluation functions with comprehensive metrics
- Visualization tools for training history
""")

def main():
    """Run all tests."""
    print("Testing fullALIGNN.py Implementation")
    print("="*50)
    
    tests = [
        ("Import Test", test_alignn_import),
        ("Model Creation Test", test_model_creation),
        ("Line Graph Creation Test", test_line_graph_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
    
    print(f"\n" + "="*50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The fullALIGNN.py implementation is working correctly.")
        show_usage_example()
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
