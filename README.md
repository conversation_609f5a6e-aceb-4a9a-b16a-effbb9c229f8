# CGN-E3: E(3)-Equivariant Crystal Graph Network with Capsule Networks

A PyTorch implementation of E(3)-equivariant graph neural networks combined with capsule networks for crystal property prediction. This model maintains rotational and translational equivariance while leveraging the dynamic routing capabilities of capsule networks.

## Features

- **E(3)-Equivariant Architecture**: Preserves rotational and translational symmetries
- **Capsule Networks**: Dynamic routing for hierarchical feature learning
- **Crystal-Specific Design**: Optimized for crystalline materials property prediction
- **Modular Implementation**: Clean, extensible codebase with separate components
- **Comprehensive Evaluation**: Built-in metrics and analysis tools

## Installation

### Requirements

- Python 3.8+
- PyTorch 1.12+
- PyTorch Geometric
- e3nn
- NumPy
- Pandas
- Scikit-learn

### Setup

1. Clone the repository:
```bash
git clone https://github.com/your-username/cgn-e3.git
cd cgn-e3
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Install the package:
```bash
pip install -e .
```

## Quick Start

### Basic Usage

```bash
python main.py --dataset_path /path/to/dataset --target_name e_form --epochs 200
```

### With Custom Configuration

```bash
python main.py --dataset_path /path/to/dataset --target_name e_form --epochs 200 --config config/large_model_config.json
```

### Evaluation Only

```bash
python main.py --dataset_path /path/to/dataset --target_name e_form --pretrained_model results_e_form/best_model_e_form.pt --eval_only
```

## ALIGNN Baseline for Comparison

This repository also includes an ALIGNN (Atomistic Line Graph Neural Network) baseline implementation for performance comparison with CGN-E3.

### ALIGNN Usage

**Train ALIGNN model:**
```bash
python main_alignn.py --dataset_path /path/to/dataset --target_name e_form --epochs 200
```

**Compare CGN-E3 vs ALIGNN:**
```bash
python examples/alignn_comparison.py --dataset_path /path/to/dataset --target_name e_form --output_dir results/comparison
```

**Key Differences:**
- **CGN-E3**: E(3)-equivariant with capsule networks
- **ALIGNN**: Line graph architecture for three-body interactions
- **Purpose**: Fair performance comparison on identical datasets

See [README_ALIGNN.md](README_ALIGNN.md) for detailed ALIGNN documentation.

## Dataset Format

The dataset should contain three files:

1. **BandgapTargets.npz**: Graph structure data with keys:
   - `graph_dict`: Dictionary of material graphs containing:
     - `node_features`: Atomic numbers
     - `cart_coords`: Cartesian coordinates
     - `neighbors`: Neighbor indices
     - `bond_lengths`: Interatomic distances
     - `type_counts`: Edge type counts
     - `neighbor_counts`: Neighbor counts per node

2. **BandgapTargets_config.json**: Configuration with:
   - `atomic_numbers`: List of atomic numbers
   - `node_vectors`: Feature vectors for each atomic number

3. **BandgapTargets.csv**: Target values with columns:
   - `mpid`: Material IDs
   - Target property columns (e.g., `e_form`, `band_gap`)

## Model Architecture

### Core Components

1. **E3EquivariantCGCNNConv**: E(3)-equivariant convolution layers
2. **RadialBasisLayer**: Distance encoding with Gaussian RBFs
3. **E3EquivariantPrimaryCapsuleLayer**: Primary capsule formation
4. **E3EquivariantSecondaryCapsuleLayer**: Dynamic routing between capsules
5. **E3LayerNorm**: E(3)-equivariant normalization

### Key Features

- **Spherical Harmonics**: For angular information encoding
- **Tensor Products**: E(3)-equivariant feature combinations
- **Dynamic Routing**: Capsule network routing algorithm
- **Attention Mechanism**: Weighted capsule aggregation

## Configuration

Model configurations are stored in JSON files in the `config/` directory:

- `default_config.json`: Standard configuration
- `small_model_config.json`: Lightweight model for testing
- `large_model_config.json`: High-capacity model for best performance

### Configuration Parameters

```json
{
  "model": {
    "hidden_channels": 256,
    "num_conv_layers": 2,
    "primary_caps": 8,
    "primary_dim": 16,
    "secondary_caps": 6,
    "secondary_dim": 16,
    "dropout_rate": 0.01
  },
  "training": {
    "batch_size": 64,
    "learning_rate": 0.005,
    "weight_decay": 1e-5,
    "early_stopping_patience": 50
  }
}
```

## Results

The model outputs several files:

- `training_metrics_{target}.csv`: Training/validation metrics per epoch
- `best_model_{target}.pt`: Best model checkpoint
- `test_metrics_{target}.csv`: Final test metrics
- `predictions_{target}.csv`: Detailed predictions and errors

## API Usage

```python
from cgn_e3.data import CartesianGraphDataset
from cgn_e3.models import E3EquivariantCrystalGNNCapsNet
from cgn_e3.utils import train_spatial_gnn, evaluate_spatial_gnn

# Load dataset
dataset = CartesianGraphDataset(dataset_path, target_name="e_form")

# Create model
model = E3EquivariantCrystalGNNCapsNet(
    node_features=dataset[0].x.size(1),
    edge_features=dataset[0].edge_attr.size(1),
    hidden_channels=256,
    num_conv_layers=2,
    primary_caps=8,
    primary_dim=16,
    secondary_caps=6,
    secondary_dim=16
)

# Train model
trained_model, losses, _, metrics = train_spatial_gnn(
    model, train_loader, optimizer, device, epochs=200
)

# Evaluate
results = evaluate_spatial_gnn(trained_model, test_loader, device)
```

## Citation

If you use this code in your research, please cite:

```bibtex
@article{cgn_e3_2024,
  title={E(3)-Equivariant Crystal Graph Networks with Capsule Networks for Materials Property Prediction},
  author={Your Name},
  journal={Journal Name},
  year={2024}
}
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Acknowledgments

- [e3nn](https://github.com/e3nn/e3nn) for E(3)-equivariant operations
- [PyTorch Geometric](https://github.com/pyg-team/pytorch_geometric) for graph neural network utilities
- Materials Project for providing crystal structure data
