"""
Evaluation utilities for CGN-E3 models.

This module contains functions for evaluating trained models and
computing various performance metrics.
"""

import torch
import torch.nn.functional as F
import numpy as np
import pandas as pd
from sklearn.metrics import r2_score


def evaluate_spatial_gnn(model, test_loader, device, results_path=None):
    """
    Evaluate a spatial GNN model on test data.
    
    Args:
        model: Trained PyTorch model
        test_loader: DataLoader for test data
        device: Device to run evaluation on (cuda/cpu)
        results_path (str, optional): Path to save test metrics CSV
        
    Returns:
        dict: Dictionary containing evaluation metrics and predictions
    """
    model.eval()
    total_mse = 0
    total_mae = 0
    predictions, targets, material_ids = [], [], []

    with torch.no_grad():
        for batch in test_loader:
            batch = batch.to(device)
            output = model(batch)
            target = batch.y

            # Accumulate losses
            total_mse += F.mse_loss(output, target, reduction='sum').item()
            total_mae += F.l1_loss(output, target, reduction='sum').item()

            # Store predictions and targets
            predictions.extend(output.cpu().numpy().flatten())
            targets.extend(target.cpu().numpy().flatten())

            # Store material IDs if available
            if hasattr(batch, 'material_id'):
                if isinstance(batch.material_id, list):
                    ids = batch.material_id
                else:
                    ids = [batch.material_id[i] for i in range(batch.num_graphs)]
                material_ids.extend(ids)

    # Calculate metrics
    num_samples = len(test_loader.dataset)
    mse = total_mse / num_samples
    mae = total_mae / num_samples
    rmse = np.sqrt(mse)
    r2 = r2_score(targets, predictions)

    # Print results
    print(f"\nTest Results:")
    print(f"  MSE  : {mse:.6f}")
    print(f"  RMSE : {rmse:.6f}")
    print(f"  MAE  : {mae:.6f}")
    print(f"  R²   : {r2:.6f}")

    # Prepare results dictionary
    results = {
        "mse": mse,
        "rmse": rmse,
        "mae": mae,
        "r2": r2,
        "predictions": predictions,
        "targets": targets,
        "material_ids": material_ids
    }

    # Save metrics to CSV if path provided
    if results_path:
        pd.DataFrame([{
            "mse": mse,
            "rmse": rmse,
            "mae": mae,
            "r2": r2
        }]).to_csv(results_path, index=False)
        print(f"Test metrics saved to {results_path}")

    return results


def calculate_additional_metrics(predictions, targets):
    """
    Calculate additional evaluation metrics.
    
    Args:
        predictions (list): Model predictions
        targets (list): True target values
        
    Returns:
        dict: Additional metrics including MAPE, max error, etc.
    """
    predictions = np.array(predictions)
    targets = np.array(targets)
    
    # Mean Absolute Percentage Error
    mape = np.mean(np.abs((targets - predictions) / targets)) * 100
    
    # Max error
    max_error = np.max(np.abs(targets - predictions))
    
    # Median Absolute Error
    median_ae = np.median(np.abs(targets - predictions))
    
    # Explained variance score
    explained_var = 1 - np.var(targets - predictions) / np.var(targets)
    
    return {
        "mape": mape,
        "max_error": max_error,
        "median_ae": median_ae,
        "explained_variance": explained_var
    }


def create_prediction_analysis(results, save_path=None):
    """
    Create detailed prediction analysis DataFrame.
    
    Args:
        results (dict): Results from evaluate_spatial_gnn
        save_path (str, optional): Path to save analysis CSV
        
    Returns:
        pd.DataFrame: Detailed analysis of predictions
    """
    predictions = np.array(results['predictions'])
    targets = np.array(results['targets'])
    material_ids = results['material_ids']
    
    # Calculate errors
    absolute_errors = np.abs(predictions - targets)
    relative_errors = np.abs((predictions - targets) / targets) * 100
    squared_errors = (predictions - targets) ** 2
    
    # Create analysis DataFrame
    analysis_df = pd.DataFrame({
        'material_id': material_ids,
        'true_value': targets,
        'predicted_value': predictions,
        'absolute_error': absolute_errors,
        'relative_error_percent': relative_errors,
        'squared_error': squared_errors
    })
    
    # Sort by absolute error (worst predictions first)
    analysis_df = analysis_df.sort_values('absolute_error', ascending=False)
    
    # Add percentile ranks
    analysis_df['error_percentile'] = analysis_df['absolute_error'].rank(pct=True) * 100
    
    if save_path:
        analysis_df.to_csv(save_path, index=False)
        print(f"Prediction analysis saved to {save_path}")
    
    return analysis_df
