#!/usr/bin/env python3
"""
Create a minimal test dataset for ALIGNN training demonstration.
"""

import os
import json
import numpy as np
import pandas as pd

def create_test_dataset(num_samples=100):
    """Create a minimal test dataset with the expected format."""
    
    print(f"Creating test dataset with {num_samples} samples...")
    
    # Define atomic numbers and their features
    atomic_numbers = [1, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 19, 20]  # Common elements
    num_elements = len(atomic_numbers)
    
    # Create random node feature vectors (simplified)
    node_vectors = np.random.randn(num_elements, 64).tolist()  # 64-dimensional features
    
    # Create config
    config = {
        "atomic_numbers": atomic_numbers,
        "node_vectors": node_vectors,
        "pos_dim": 3
    }
    
    # Save config
    with open("BandgapTargets_config.json", "w") as f:
        json.dump(config, f, indent=2)
    print("✅ Created BandgapTargets_config.json")
    
    # Create graph data
    graph_dict = {}
    material_ids = []
    targets = []
    
    for i in range(num_samples):
        material_id = f"mp-{i+1000}"
        material_ids.append(material_id)
        
        # Create a random small crystal structure
        num_atoms = np.random.randint(4, 12)  # 4-11 atoms
        
        # Random atomic numbers from our list
        atom_types = np.random.choice(atomic_numbers, size=num_atoms)
        
        # Random 3D coordinates (simulating a crystal structure)
        cart_coords = np.random.uniform(-5, 5, size=(num_atoms, 3))
        
        # Create graph structure
        graph = {
            'node_features': atom_types.tolist(),  # Use this key name
            'cart_coords': cart_coords.tolist(),
            'num_atoms': num_atoms
        }
        
        graph_dict[material_id] = graph
        
        # Create a random target value (formation energy)
        target = np.random.normal(0, 2)  # Random formation energy
        targets.append(target)
    
    # Save graph data
    np.savez("BandgapTargets.npz", graph_dict=graph_dict)
    print("✅ Created BandgapTargets.npz")
    
    # Create CSV with targets
    df = pd.DataFrame({
        'mpid': material_ids,
        'e_form': targets
    })
    df.to_csv("BandgapTargets.csv", index=False)
    print("✅ Created BandgapTargets.csv")
    
    print(f"\nTest dataset created successfully!")
    print(f"- {num_samples} materials")
    print(f"- {num_elements} element types")
    print(f"- Target statistics: mean={np.mean(targets):.3f}, std={np.std(targets):.3f}")
    
    return True

def verify_dataset():
    """Verify the created dataset can be loaded."""
    print("\nVerifying dataset...")
    
    try:
        # Test loading
        with np.load("BandgapTargets.npz", allow_pickle=True) as data:
            graph_dict = data['graph_dict'].item()
        
        with open("BandgapTargets_config.json") as f:
            config = json.load(f)
        
        df = pd.read_csv("BandgapTargets.csv")
        
        print(f"✅ Successfully loaded all files")
        print(f"✅ Graphs: {len(graph_dict)}")
        print(f"✅ Config elements: {len(config['atomic_numbers'])}")
        print(f"✅ Targets: {len(df)}")
        
        # Check first graph structure
        first_key = list(graph_dict.keys())[0]
        first_graph = graph_dict[first_key]
        print(f"✅ First graph keys: {list(first_graph.keys())}")
        print(f"✅ First graph has {len(first_graph['node_features'])} atoms")
        
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

if __name__ == "__main__":
    print("Test Dataset Creator for ALIGNN")
    print("="*40)
    
    # Create test dataset
    if create_test_dataset(num_samples=50):  # Small dataset for testing
        if verify_dataset():
            print("\n🎉 Test dataset ready for ALIGNN training!")
            print("\nYou can now run:")
            print("python src/alignn_baseline/fullALIGNN.py --dataset_path . --target_name e_form --num_epochs 5 --batch_size 4")
        else:
            print("\n❌ Dataset verification failed")
    else:
        print("\n❌ Dataset creation failed")
