"""
Pytest configuration and fixtures for CGN-E3 tests.
"""

import pytest
import torch
import numpy as np


@pytest.fixture(autouse=True)
def set_random_seeds():
    """Set random seeds for reproducible tests."""
    torch.manual_seed(42)
    np.random.seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
        torch.cuda.manual_seed_all(42)


@pytest.fixture
def device():
    """Provide device for testing."""
    return torch.device('cpu')  # Use CPU for tests to ensure compatibility


@pytest.fixture
def small_model_config():
    """Provide small model configuration for testing."""
    return {
        'node_features': 5,
        'edge_features': 2,
        'hidden_channels': 16,
        'num_conv_layers': 1,
        'primary_caps': 2,
        'primary_dim': 4,
        'secondary_caps': 2,
        'secondary_dim': 4,
        'dropout_rate': 0.0
    }
