{"model": {"hidden_channels": 256, "num_conv_layers": 2, "primary_caps": 8, "primary_dim": 16, "secondary_caps": 6, "secondary_dim": 16, "dropout_rate": 0.01}, "training": {"batch_size": 64, "learning_rate": 0.005, "weight_decay": 1e-05, "early_stopping_patience": 50, "grad_clip_norm": 0.5}, "scheduler": {"type": "ReduceLROnPlateau", "mode": "min", "factor": 0.5, "patience": 15, "min_lr": 1e-06, "verbose": true}, "data": {"train_split": 0.8, "val_split": 0.1, "test_split": 0.1, "random_seed": 42}, "conv_layer": {"num_rbf": 16, "cutoff": 10.0, "lmax": 1}, "capsule": {"routing_iterations": 2}}