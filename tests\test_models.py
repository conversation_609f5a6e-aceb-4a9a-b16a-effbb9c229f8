"""
Tests for model components.
"""

import pytest
import torch
import torch.nn as nn
from torch_geometric.data import Data, Batch

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from cgn_e3.models import (
    RadialBasisLayer,
    E3EquivariantCGCNNConv,
    E3EquivariantPrimaryCapsuleLayer,
    E3EquivariantSecondaryCapsuleLayer,
    E3LayerNorm,
    E3EquivariantCrystalGNNCapsNet
)


class TestRadialBasisLayer:
    """Test cases for RadialBasisLayer."""
    
    def test_initialization(self):
        """Test layer initialization."""
        rbf = RadialBasisLayer(num_rbf=16, cutoff=10.0)
        assert rbf.num_rbf == 16
        assert rbf.cutoff == 10.0
        assert rbf.centers.shape == (16,)
        assert rbf.widths.shape == (16,)
    
    def test_forward_pass(self):
        """Test forward pass."""
        rbf = RadialBasisLayer(num_rbf=8, cutoff=5.0)
        distances = torch.tensor([[1.0], [2.5], [4.0], [6.0]])  # Last one exceeds cutoff
        
        output = rbf(distances)
        
        assert output.shape == (4, 8)
        assert torch.all(output >= 0)  # RBF outputs should be non-negative
        assert torch.all(output <= 1)  # With envelope, should be <= 1


class TestE3EquivariantCGCNNConv:
    """Test cases for E3EquivariantCGCNNConv."""
    
    def create_sample_data(self):
        """Create sample data for testing."""
        num_nodes = 5
        channels = 32
        
        x_scalar = torch.randn(num_nodes, channels)
        x_vector = torch.randn(num_nodes, channels, 3)
        edge_index = torch.tensor([[0, 1, 2, 3], [1, 2, 3, 4]], dtype=torch.long)
        edge_attr = torch.randn(4, 2)  # [bond_length, edge_type]
        pos = torch.randn(num_nodes, 3)
        
        return x_scalar, x_vector, edge_index, edge_attr, pos
    
    def test_initialization(self):
        """Test layer initialization."""
        conv = E3EquivariantCGCNNConv(channels=32)
        assert conv.channels == 32
        assert conv.num_rbf == 16
        assert conv.cutoff == 10.0
        assert conv.lmax == 1
    
    def test_forward_pass(self):
        """Test forward pass."""
        conv = E3EquivariantCGCNNConv(channels=32)
        x_scalar, x_vector, edge_index, edge_attr, pos = self.create_sample_data()
        
        out_scalar, out_vector = conv(x_scalar, x_vector, edge_index, edge_attr, pos)
        
        assert out_scalar.shape == x_scalar.shape
        assert out_vector.shape == x_vector.shape
    
    def test_equivariance(self):
        """Test E(3) equivariance property."""
        conv = E3EquivariantCGCNNConv(channels=16)
        x_scalar, x_vector, edge_index, edge_attr, pos = self.create_sample_data()
        
        # Apply random rotation
        R = torch.randn(3, 3)
        R = torch.qr(R)[0]  # Orthogonal matrix
        
        # Rotate positions and vectors
        pos_rot = pos @ R.T
        x_vector_rot = x_vector @ R.T
        
        # Forward pass on original and rotated data
        out_scalar1, out_vector1 = conv(x_scalar, x_vector, edge_index, edge_attr, pos)
        out_scalar2, out_vector2 = conv(x_scalar, x_vector_rot, edge_index, edge_attr, pos_rot)
        
        # Scalar features should be invariant
        assert torch.allclose(out_scalar1, out_scalar2, atol=1e-5)
        
        # Vector features should be equivariant
        assert torch.allclose(out_vector1 @ R.T, out_vector2, atol=1e-5)


class TestE3LayerNorm:
    """Test cases for E3LayerNorm."""
    
    def test_initialization(self):
        """Test layer initialization."""
        norm = E3LayerNorm(channels=32)
        assert norm.channels == 32
        assert not norm.scalar_only
        assert norm.scalar_scale.shape == (32,)
        assert norm.scalar_bias.shape == (32,)
        assert norm.vector_scale.shape == (32,)
    
    def test_forward_pass(self):
        """Test forward pass."""
        norm = E3LayerNorm(channels=16)
        x_scalar = torch.randn(10, 16)
        x_vector = torch.randn(10, 16, 3)
        
        out_scalar, out_vector = norm(x_scalar, x_vector)
        
        assert out_scalar.shape == x_scalar.shape
        assert out_vector.shape == x_vector.shape
    
    def test_scalar_only_mode(self):
        """Test scalar-only normalization."""
        norm = E3LayerNorm(channels=16, scalar_only=True)
        x_scalar = torch.randn(10, 16)
        x_vector = torch.randn(10, 16, 3)
        
        out_scalar, out_vector = norm(x_scalar, x_vector)
        
        assert out_scalar.shape == x_scalar.shape
        assert torch.allclose(out_vector, x_vector)  # Vector unchanged


class TestE3EquivariantCrystalGNNCapsNet:
    """Test cases for the main model."""
    
    def create_sample_batch(self):
        """Create sample batch data."""
        data_list = []
        for i in range(2):  # Batch of 2 graphs
            num_nodes = 5 + i  # Different sizes
            data = Data(
                x=torch.randn(num_nodes, 10),  # Node features
                edge_index=torch.randint(0, num_nodes, (2, num_nodes)),
                edge_attr=torch.randn(num_nodes, 2),  # Edge features
                pos=torch.randn(num_nodes, 3),
                y=torch.randn(1, 1)
            )
            data_list.append(data)
        
        return Batch.from_data_list(data_list)
    
    def test_model_initialization(self):
        """Test model initialization."""
        model = E3EquivariantCrystalGNNCapsNet(
            node_features=10,
            edge_features=2,
            hidden_channels=32,
            num_conv_layers=2,
            primary_caps=4,
            primary_dim=8,
            secondary_caps=3,
            secondary_dim=8
        )
        
        # Check that all components are initialized
        assert len(model.convs) == 2
        assert len(model.layer_norms) == 2
        assert model.primary_caps.out_caps == 4
        assert model.secondary_caps.out_caps == 3
    
    def test_forward_pass(self):
        """Test model forward pass."""
        model = E3EquivariantCrystalGNNCapsNet(
            node_features=10,
            edge_features=2,
            hidden_channels=32,
            num_conv_layers=1,
            primary_caps=4,
            primary_dim=8,
            secondary_caps=3,
            secondary_dim=8
        )
        
        batch = self.create_sample_batch()
        output = model(batch)
        
        assert output.shape == (2, 1)  # Batch size 2, 1 output per graph
    
    def test_model_parameters(self):
        """Test model parameter count."""
        model = E3EquivariantCrystalGNNCapsNet(
            node_features=10,
            edge_features=2,
            hidden_channels=16,
            num_conv_layers=1,
            primary_caps=2,
            primary_dim=4,
            secondary_caps=2,
            secondary_dim=4
        )
        
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        assert total_params > 0
        assert trainable_params > 0
        assert trainable_params <= total_params


if __name__ == "__main__":
    pytest.main([__file__])
