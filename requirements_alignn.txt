# ALIGNN Baseline Requirements
# Compatible with CGN-E3 requirements

# Core dependencies (same as CGN-E3)
torch>=1.12.0
torch-geometric>=2.1.0
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# Visualization and analysis
matplotlib>=3.5.0
seaborn>=0.11.0

# Utilities
tqdm>=4.62.0
scipy>=1.7.0

# Development and testing
pytest>=6.2.0
pytest-cov>=3.0.0

# Optional: For advanced features
# dgl>=0.9.0  # Alternative graph library
# tensorboard>=2.8.0  # For logging
# wandb>=0.12.0  # For experiment tracking
