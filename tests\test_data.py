"""
Tests for data handling modules.
"""

import pytest
import numpy as np
import torch
import tempfile
import os
import json
import pandas as pd

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from cgn_e3.data import Graph, CartesianGraphDataset


class TestGraph:
    """Test cases for the Graph class."""
    
    def create_sample_graph_data(self):
        """Create sample graph data for testing."""
        return {
            'node_features': [1, 6, 8],  # H, C, O
            'type_counts': [2, 1],  # 2 edges of type 0, 1 edge of type 1
            'neighbor_counts': [[1, 1, 1], [0, 1, 0]],  # neighbors per node per edge type
            'neighbors': [1, 2, 0],  # neighbor indices
            'bond_lengths': [1.0, 1.5, 1.2],  # bond distances
            'cart_coords': [[0.0, 0.0, 0.0], [1.0, 0.0, 0.0], [0.0, 1.0, 0.0]]
        }
    
    def test_graph_initialization(self):
        """Test basic graph initialization."""
        graph_data = self.create_sample_graph_data()
        graph = Graph(graph_data)
        
        assert len(graph.nodes) == 3
        assert len(graph.cart_coords) == 3
        assert graph.cart_coords.shape[1] == 3
        assert graph.edge_index.shape[0] == 2
        assert graph.edge_attr.shape[1] == 2
    
    def test_graph_validation(self):
        """Test graph data validation."""
        # Test missing required field
        incomplete_data = {'node_features': [1, 2]}
        with pytest.raises(ValueError, match="Missing required graph data field"):
            Graph(incomplete_data)
        
        # Test coordinate dimension mismatch
        bad_coords_data = self.create_sample_graph_data()
        bad_coords_data['cart_coords'] = [[0.0, 0.0], [1.0, 0.0]]  # 2D instead of 3D
        with pytest.raises(ValueError, match="Coordinates must be 3-dimensional"):
            Graph(bad_coords_data)
    
    def test_edge_creation(self):
        """Test edge index and attribute creation."""
        graph_data = self.create_sample_graph_data()
        graph = Graph(graph_data)
        
        # Check edge index shape and content
        assert graph.edge_index.shape == (2, 3)
        assert torch.all(graph.edge_index >= 0)
        assert torch.all(graph.edge_index < len(graph.nodes))
        
        # Check edge attributes
        assert graph.edge_attr.shape == (3, 2)
        assert torch.all(graph.edge_attr[:, 0] > 0)  # Bond lengths should be positive


class TestCartesianGraphDataset:
    """Test cases for the CartesianGraphDataset class."""
    
    def create_test_dataset(self, temp_dir):
        """Create a minimal test dataset."""
        # Create NPZ file
        graph_dict = {
            'test_material_1': {
                'node_features': [1, 6],
                'type_counts': [1],
                'neighbor_counts': [[1, 1]],
                'neighbors': [1],
                'bond_lengths': [1.0],
                'cart_coords': [[0.0, 0.0, 0.0], [1.0, 0.0, 0.0]]
            },
            'test_material_2': {
                'node_features': [6, 8],
                'type_counts': [1],
                'neighbor_counts': [[1, 1]],
                'neighbors': [1],
                'bond_lengths': [1.2],
                'cart_coords': [[0.0, 0.0, 0.0], [1.2, 0.0, 0.0]]
            }
        }
        
        npz_path = os.path.join(temp_dir, "BandgapTargets.npz")
        np.savez(npz_path, graph_dict=graph_dict)
        
        # Create config file
        config = {
            "atomic_numbers": [1, 6, 8],
            "node_vectors": [[1.0, 0.0], [0.0, 1.0], [0.5, 0.5]],
            "pos_dim": 3
        }
        
        config_path = os.path.join(temp_dir, "BandgapTargets_config.json")
        with open(config_path, 'w') as f:
            json.dump(config, f)
        
        # Create CSV file
        df = pd.DataFrame({
            'mpid': ['test_material_1', 'test_material_2'],
            'e_form': [-1.5, -2.0],
            'band_gap': [1.0, 2.0]
        })
        
        csv_path = os.path.join(temp_dir, "BandgapTargets.csv")
        df.to_csv(csv_path, index=False)
    
    def test_dataset_loading(self):
        """Test dataset loading and initialization."""
        with tempfile.TemporaryDirectory() as temp_dir:
            self.create_test_dataset(temp_dir)
            
            dataset = CartesianGraphDataset(temp_dir, target_name="e_form")
            
            assert len(dataset) == 2
            assert dataset.target_name == "e_form"
            assert len(dataset.atomic_numbers) == 3
            assert dataset.n_node_feat == 2
    
    def test_dataset_getitem(self):
        """Test dataset item retrieval."""
        with tempfile.TemporaryDirectory() as temp_dir:
            self.create_test_dataset(temp_dir)
            
            dataset = CartesianGraphDataset(temp_dir, target_name="e_form")
            data = dataset[0]
            
            # Check data structure
            assert hasattr(data, 'x')
            assert hasattr(data, 'edge_index')
            assert hasattr(data, 'edge_attr')
            assert hasattr(data, 'pos')
            assert hasattr(data, 'y')
            assert hasattr(data, 'material_id')
            
            # Check shapes
            assert data.x.shape[1] == 2  # node features
            assert data.pos.shape[1] == 3  # 3D coordinates
            assert data.y.shape == (1, 1)  # target value
    
    def test_invalid_target(self):
        """Test handling of invalid target names."""
        with tempfile.TemporaryDirectory() as temp_dir:
            self.create_test_dataset(temp_dir)
            
            with pytest.raises(ValueError, match="Target column 'invalid_target' not found"):
                CartesianGraphDataset(temp_dir, target_name="invalid_target")


if __name__ == "__main__":
    pytest.main([__file__])
