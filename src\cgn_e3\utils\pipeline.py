"""
Complete training and evaluation pipeline for CGN-E3 models.

This module contains the main pipeline function that orchestrates
the entire training and evaluation process.
"""

import os
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from torch.utils.data import DataLoader, random_split
from torch_geometric.data import Batch

from ..data import CartesianGraphDataset
from ..models import E3EquivariantCrystalGNNCapsNet
from .training import train_spatial_gnn
from .evaluation import evaluate_spatial_gnn


def run_spatial_gnn_capsnet(dataset_path, target_name, epochs, 
                           pretrained_model_path=None, output_dir=None,
                           model_config=None):
    """
    Complete pipeline for training and evaluating CGN-E3 models.
    
    Args:
        dataset_path (str): Path to dataset directory
        target_name (str): Name of target property to predict
        epochs (int): Number of training epochs
        pretrained_model_path (str, optional): Path to pretrained model
        output_dir (str, optional): Directory to save results
        model_config (dict, optional): Model configuration parameters
        
    Returns:
        tuple: (trained_model, test_results)
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Set up output directory
    if output_dir is None:
        output_dir = f"results_{target_name}"
    os.makedirs(output_dir, exist_ok=True)

    # Define file paths
    metrics_path = os.path.join(output_dir, f"training_metrics_{target_name}.csv")
    checkpoint_path = os.path.join(output_dir, f"best_model_{target_name}.pt")
    test_metrics_path = os.path.join(output_dir, f"test_metrics_{target_name}.csv")
    results_file = os.path.join(output_dir, f"predictions_{target_name}.csv")

    # Load dataset
    print(f"Loading dataset from {dataset_path}")
    dataset = CartesianGraphDataset(dataset_path, target_name=target_name)
    print(f"Dataset loaded with {len(dataset)} samples")

    # Split dataset
    train_size = int(0.8 * len(dataset))
    test_size = len(dataset) - train_size
    train_dataset, test_dataset = random_split(
        dataset, [train_size, test_size],
        generator=torch.Generator().manual_seed(42)
    )
    print(f"Split: {train_size} train, {test_size} test")

    # Create data loaders
    batch_size = 64
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, 
        shuffle=True, collate_fn=Batch.from_data_list
    )
    test_loader = DataLoader(
        test_dataset, batch_size=batch_size, 
        collate_fn=Batch.from_data_list
    )

    # Set default model configuration
    default_config = {
        'hidden_channels': 256,
        'num_conv_layers': 2,
        'primary_caps': 8,
        'primary_dim': 16,
        'secondary_caps': 6,
        'secondary_dim': 16,
        'dropout_rate': 0.01
    }
    
    if model_config:
        default_config.update(model_config)

    # Initialize model
    print("Initializing E3-Equivariant Crystal GNN + Capsule Network model")
    model = E3EquivariantCrystalGNNCapsNet(
        node_features=dataset[0].x.size(1),
        edge_features=dataset[0].edge_attr.size(1),
        **default_config
    ).to(device)

    # Load pretrained model if provided
    if pretrained_model_path and os.path.exists(pretrained_model_path):
        print(f"Loading pretrained model from {pretrained_model_path}")
        checkpoint = torch.load(pretrained_model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print("Pretrained model loaded.")
    else:
        print("Training from scratch.")

    # Print model info
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")

    # Initialize model weights
    for m in model.modules():
        if isinstance(m, nn.Linear):
            nn.init.xavier_uniform_(m.weight)
            if m.bias is not None:
                nn.init.zeros_(m.bias)

    # Set up optimizer and scheduler
    optimizer = torch.optim.AdamW(
        model.parameters(), lr=5e-3, weight_decay=1e-5
    )
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=15, 
        verbose=True, min_lr=1e-6
    )

    # Train model
    print(f"Training for {epochs} epochs...")
    model, train_losses, val_losses, _ = train_spatial_gnn(
        model, train_loader, optimizer, device,
        epochs=epochs,
        scheduler=scheduler,
        early_stopping_patience=50,
        checkpoint_path=checkpoint_path,
        metrics_path=metrics_path
    )

    # Evaluate on test set
    print("Evaluating on test set...")
    test_results = evaluate_spatial_gnn(
        model, test_loader, device, results_path=test_metrics_path
    )

    # Save detailed predictions
    results_df = pd.DataFrame({
        'material_id': test_results['material_ids'],
        'true_value': test_results['targets'],
        'predicted_value': test_results['predictions'],
        'absolute_error': np.abs(
            np.array(test_results['predictions']) - 
            np.array(test_results['targets'])
        )
    })
    results_df.to_csv(results_file, index=False)
    print(f"Prediction results saved to {results_file}")

    return model, test_results


def load_trained_model(checkpoint_path, model_config, device=None):
    """
    Load a trained model from checkpoint.
    
    Args:
        checkpoint_path (str): Path to model checkpoint
        model_config (dict): Model configuration parameters
        device: Device to load model on
        
    Returns:
        torch.nn.Module: Loaded model
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create model with same configuration
    model = E3EquivariantCrystalGNNCapsNet(**model_config).to(device)
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print(f"Model loaded from {checkpoint_path}")
    print(f"Training epoch: {checkpoint['epoch']}")
    print(f"Best validation loss: {checkpoint['loss']:.6f}")
    
    return model
