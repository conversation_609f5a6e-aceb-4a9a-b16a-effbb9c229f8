"""
Main E(3)-equivariant crystal graph network with capsule networks.

This module contains the complete model architecture that combines
E(3)-equivariant convolutions with capsule networks for crystal
property prediction.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

from .conv_layers import E3EquivariantCGCNNConv
from .capsule_layers import E3EquivariantPrimaryCapsuleLayer, E3EquivariantSecondaryCapsuleLayer
from .normalization import E3LayerNorm


class E3EquivariantCrystalGNNCapsNet(nn.Module):
    """
    E(3)-Equivariant Crystal Graph Neural Network with Capsule Networks.
    
    This model combines E(3)-equivariant graph convolutions with capsule networks
    to predict crystal properties while maintaining rotational and translational
    equivariance.
    
    Args:
        node_features (int): Number of input node features
        edge_features (int): Number of input edge features
        hidden_channels (int): Number of hidden channels
        num_conv_layers (int): Number of convolution layers
        primary_caps (int): Number of primary capsules
        primary_dim (int): Dimension of primary capsules
        secondary_caps (int): Number of secondary capsules
        secondary_dim (int): Dimension of secondary capsules
        dropout_rate (float): Dropout rate for regularization
    """
    
    def __init__(self, node_features, edge_features, hidden_channels,
                 num_conv_layers, primary_caps, primary_dim,
                 secondary_caps, secondary_dim, dropout_rate=0.0):
        super().__init__()

        # Node embedding - invariant
        self.node_embedding = nn.Sequential(
            nn.Linear(node_features, hidden_channels),
            nn.BatchNorm1d(hidden_channels),
            nn.ReLU(),
            nn.Linear(hidden_channels, hidden_channels),
            nn.BatchNorm1d(hidden_channels),
            nn.ReLU(),
        )

        # Edge embedding - invariant
        self.edge_embedding = nn.Sequential(
            nn.Linear(edge_features, hidden_channels),
            nn.ReLU(),
            nn.Linear(hidden_channels, hidden_channels),
            nn.ReLU()
        )

        # Initialize vector features - equivariant
        self.init_vector_features = nn.Linear(hidden_channels, hidden_channels)

        # E(3)-equivariant convolution layers
        self.convs = nn.ModuleList([
            E3EquivariantCGCNNConv(hidden_channels)
            for _ in range(num_conv_layers)
        ])

        # Layer normalization - equivariant
        self.layer_norms = nn.ModuleList([
            E3LayerNorm(hidden_channels)
            for _ in range(num_conv_layers)
        ])

        # Capsule layers - equivariant
        self.primary_caps = E3EquivariantPrimaryCapsuleLayer(
            scalar_features=hidden_channels,
            vector_features=hidden_channels,
            out_caps=primary_caps,
            caps_dim=primary_dim
        )

        self.secondary_caps = E3EquivariantSecondaryCapsuleLayer(
            in_dim=primary_dim,
            out_caps=secondary_caps,
            out_dim=secondary_dim,
            routing_iterations=2
        )

        # Attention network - invariant
        self.attn_net = nn.Sequential(
            nn.Linear(secondary_dim // 2, 1)
        )

        # Final predictor (invariant)
        self.predictor = nn.Sequential(
            nn.Linear(secondary_dim, hidden_channels//2),
            nn.BatchNorm1d(hidden_channels//2),
            nn.ReLU(),
            nn.Linear(hidden_channels//2, hidden_channels//4),
            nn.BatchNorm1d(hidden_channels//4),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_channels//4, 1)
        )

        self.dropout = nn.Dropout(dropout_rate)

        # Activation functions
        self.scalar_act = nn.ReLU()
        self.gate_act = nn.Sigmoid()

    def forward(self, data):
        """
        Forward pass of the model.
        
        Args:
            data: PyTorch Geometric Data object containing:
                - x: Node features [num_nodes, node_features]
                - edge_index: Edge connectivity [2, num_edges]
                - edge_attr: Edge attributes [num_edges, edge_features]
                - pos: Node positions [num_nodes, 3]
                - batch: Batch indices [num_nodes]
                
        Returns:
            torch.Tensor: Predicted property values [batch_size, 1]
        """
        x, edge_index, edge_attr, pos = data.x, data.edge_index, data.edge_attr, data.pos
        batch = (data.batch if hasattr(data, 'batch') 
                else torch.zeros(x.size(0), dtype=torch.long, device=x.device))

        # Process node features (invariant)
        x_scalar = self.node_embedding(x)
        edge_attr = self.edge_embedding(edge_attr)

        # Initialize vector features (equivariant)
        vector_weights = self.init_vector_features(x_scalar)
        # Create vector features with shape [batch_size, hidden_channels, 3]
        x_vector = torch.zeros(
            x_scalar.size(0), vector_weights.size(1), 3, 
            device=x_scalar.device
        )
        
        # Use position information to initialize vectors
        for i in range(x_scalar.size(0)):
            x_vector[i] = vector_weights[i].unsqueeze(-1) * pos[i].unsqueeze(0)

        # Apply E(3)-equivariant convolutions
        for i, (conv, norm) in enumerate(zip(self.convs, self.layer_norms)):
            x_scalar_res = x_scalar
            x_vector_res = x_vector

            x_scalar, x_vector = conv(x_scalar, x_vector, edge_index, edge_attr, pos)
            x_scalar, x_vector = norm(x_scalar, x_vector)
            x_scalar_act = self.scalar_act(x_scalar)

            # Create gates from scalar features
            gates = self.gate_act(x_scalar)

            # Apply gates to vector features (equivariant operation)
            x_vector = x_vector * gates.unsqueeze(-1)

            # Residual connections
            if i > 0:
                x_scalar = x_scalar_act + x_scalar_res
                x_vector = x_vector + x_vector_res
            else:
                x_scalar = x_scalar_act

        # Apply capsule layers (equivariant)
        primary_caps, primary_vectors = self.primary_caps(x_scalar, x_vector)

        # Ensure primary_vectors has the right shape for secondary_caps
        if primary_vectors is not None:
            # Get the shape of primary_vectors
            if primary_vectors.dim() == 3:  # [batch_size, vector_features, 3]
                # Expand to match primary_caps dimension
                primary_caps_size = primary_caps.size(1)
                primary_vectors = primary_vectors.unsqueeze(1).expand(
                    -1, primary_caps_size, -1, -1
                )

        # Apply secondary capsule layer
        secondary_caps, _ = self.secondary_caps(primary_caps, primary_vectors, batch)

        # Use only scalar part for attention
        scalar_part = secondary_caps[:, :, :secondary_caps.size(2)//2]
        attn_weights = F.softmax(self.attn_net(scalar_part), dim=1)

        # Weight capsules by attention
        weighted_caps = (attn_weights * secondary_caps).sum(dim=1)

        # Final prediction
        return self.predictor(weighted_caps)
