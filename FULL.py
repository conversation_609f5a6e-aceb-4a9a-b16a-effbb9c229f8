import os
import json
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from torch.utils.data import Dataset, DataLoader, SubsetRandomSampler, random_split
from torch_geometric.nn import MessagePassing
from torch_geometric.data import Data, Batch
from torch_geometric.utils import scatter, to_dense_batch
from sklearn.metrics import mean_absolute_error, r2_score
from sklearn.metrics import mean_absolute_error
from torch.optim.lr_scheduler import ReduceLROnPlateau
import torch_geometric.transforms as T
from e3nn import o3
from e3nn.o3 import FullyConnectedTensorProduct
from e3nn.nn import Gate, BatchNorm

from e3nn.o3 import spherical_harmonics
import warnings


warnings.filterwarnings('ignore')

class Graph:
    def __init__(self, graph_data):
        try:
            self.nodes = graph_data['node_features']
            self.type_counts = graph_data['type_counts']
            self.neighbor_counts = graph_data['neighbor_counts']
            self.neighbors = graph_data['neighbors']
            self.bond_lengths = graph_data['bond_lengths']
            self.cart_coords = graph_data['cart_coords']


            self.nodes = np.array(self.nodes)
            self.type_counts = np.array(self.type_counts)
            self.neighbor_counts = np.array(self.neighbor_counts)
            self.neighbors = np.array(self.neighbors)
            self.bond_lengths = np.array(self.bond_lengths)
            self.cart_coords = np.array(self.cart_coords)

        except KeyError as e:
            raise ValueError(f"Missing required graph data field: {str(e)}")

        if len(self.nodes) != len(self.cart_coords):
            raise ValueError(f"Number of nodes ({len(self.nodes)}) doesn't match coordinate count ({len(self.cart_coords)})")
        if self.cart_coords.shape[1] != 3:
            raise ValueError("Coordinates must be 3-dimensional")
        if len(self.bond_lengths) != len(self.neighbors):
            raise ValueError(f"Bond lengths count ({len(self.bond_lengths)}) must match neighbor count ({len(self.neighbors)})")

        self.edge_attr = self._create_edge_attributes()
        self.edge_index = self._create_edge_index()

    def _create_edge_attributes(self):
        """Create edge attributes with bond lengths and types"""
        edge_types = []
        for edge_type, count in enumerate(self.type_counts):
            edge_types.extend([edge_type] * count)

        if len(edge_types) != len(self.bond_lengths):
            raise ValueError(f"Edge type count ({len(edge_types)}) doesn't match bond lengths count ({len(self.bond_lengths)})")

        return torch.tensor(
            np.column_stack([self.bond_lengths, edge_types]),
            dtype=torch.float32
        )

    def _create_edge_index(self):
        """Create edge index with source and target nodes"""
        edge_sources = []
        num_edge_labels = len(self.type_counts)
        neighbor_counts = self.neighbor_counts.reshape(num_edge_labels, -1)

        for edge_type in range(num_edge_labels):
            for node_idx, count in enumerate(neighbor_counts[edge_type]):
                edge_sources.extend([node_idx] * count)


        edge_targets = []
        start_idx = 0
        for count in self.type_counts:
            end_idx = start_idx + count
            edge_targets.extend(self.neighbors[start_idx:end_idx])
            start_idx = end_idx

        if len(edge_sources) != len(edge_targets):
            raise ValueError(f"Edge sources count ({len(edge_sources)}) and targets count ({len(edge_targets)}) mismatch")
        if len(edge_sources) != len(self.bond_lengths):
            raise ValueError(f"Edge count ({len(edge_sources)}) doesn't match bond length count ({len(self.bond_lengths)})")

        return torch.tensor([edge_sources, edge_targets], dtype=torch.long).contiguous()

class CartesianGraphDataset(Dataset):
    def __init__(self, path, target_name):
        super().__init__()
        self.path = path
        self.target_name = target_name

        self._load_graph_data()
        self._load_config()
        self._load_targets()

        if len(self.graph_data) != len(self.targets):
            raise ValueError(
                f"Graph count ({len(self.graph_data)}) doesn't match "
                f"target count ({len(self.targets)})"
            )

    def _load_graph_data(self):
        """Load and validate graph data from NPZ file"""
        npz_path = os.path.join(self.path, "BandgapTargets.npz")
        try:
            with np.load(npz_path, allow_pickle=True) as data:
                graph_dict = data['graph_dict'].item()
                self.graph_names = list(graph_dict.keys())
                self.graph_data = []

                for name, graph in graph_dict.items():
                    try:
                        if 'cart_coords' not in graph:
                            raise ValueError(f"Missing cart_coords in graph {name}")
                        self.graph_data.append(Graph(graph))
                    except ValueError as e:
                        print(f"Skipping invalid graph {name}: {str(e)}")
                        continue

                if not self.graph_data:
                    raise ValueError("No valid graphs found in NPZ file")
        except Exception as e:
            raise RuntimeError(f"Error loading graph data: {str(e)}")

    def _load_config(self):
        """Load and validate configuration"""
        config_path = os.path.join(self.path, "BandgapTargets_config.json")
        try:
            with open(config_path) as f:
                config = json.load(f)

            self.atomic_numbers = config["atomic_numbers"]
            self.node_vectors = np.array(config["node_vectors"])
            self.n_node_feat = len(self.node_vectors[0])
            self.pos_dim = config.get("pos_dim", 3)
            self.atomic_to_idx = {num: idx for idx, num in enumerate(self.atomic_numbers)}
            if len(self.atomic_to_idx) != len(self.atomic_numbers):
                raise ValueError("Duplicate atomic numbers in config")

        except Exception as e:
            raise RuntimeError(f"Error loading config: {str(e)}")

    def _load_targets(self):
        targets_path = os.path.join(self.path, "BandgapTargets.csv")
        try:
            df = pd.read_csv(targets_path)
            if self.target_name not in df.columns:
                raise ValueError(f"Target column '{self.target_name}' not found in CSV")

            self.targets = df[self.target_name].values
            if len(self.targets) == 0:
                raise ValueError("No targets found in CSV file")

            self.graph_names = df['mpid'].values.tolist()

        except Exception as e:
            raise RuntimeError(f"Error loading targets: {str(e)}")

    def __len__(self):
        return len(self.graph_data)

    def __getitem__(self, index):
        """Create PyG Data object with all features"""
        graph = self.graph_data[index]


        node_features = np.zeros((len(graph.nodes), self.n_node_feat))
        for i, atomic_num in enumerate(graph.nodes):
            idx = self.atomic_to_idx[atomic_num]
            node_features[i] = self.node_vectors[idx]

        data = Data(
            x=torch.tensor(node_features, dtype=torch.float32),
            edge_index=graph.edge_index,
            edge_attr=graph.edge_attr,
            pos=graph.cart_coords.clone().detach() if isinstance(graph.cart_coords, torch.Tensor)
                else torch.tensor(graph.cart_coords, dtype=torch.float32),
            y=torch.tensor([[self.targets[index]]], dtype=torch.float32),
            material_id=self.graph_names[index]
        )
        return data


from e3nn import o3
from e3nn.o3 import Irreps, spherical_harmonics
class E3EquivariantCGCNNConv(nn.Module):
    def __init__(self, channels, num_rbf=16, cutoff=10.0, lmax=1):
        super().__init__()
        self.channels = channels
        self.num_rbf = num_rbf
        self.cutoff = cutoff
        self.lmax = lmax

        # Radial basis functions (invariant to rotations)
        self.rbf = RadialBasisLayer(num_rbf, cutoff)

        # Scalar network (invariant)
        self.scalar_mlp = nn.Sequential(
            nn.Linear(channels * 2 + num_rbf, channels),
            nn.SiLU(),
            nn.Linear(channels, channels)
        )

        # Equivariant vector operations
        # Input irreps: scalar (l=0) + vector (l=1)
        irreps_in1 = o3.Irreps(f"{channels}x0e + {channels}x1e")
        # Spherical harmonics irreps
        irreps_in2 = o3.Irreps([(1, (l, 1)) for l in range(lmax + 1)])
        # Output irreps: same structure as input1
        irreps_out = o3.Irreps(f"{channels}x0e + {channels}x1e")

        # Tensor product for equivariant message passing
        self.tp = FullyConnectedTensorProduct(
            irreps_in1=irreps_in1,
            irreps_in2=irreps_in2,
            irreps_out=irreps_out,
            internal_weights=True
        )

        # Scalar activation and gate activation
        self.scalar_act = nn.SiLU()
        self.gate_act = nn.Sigmoid()

    def forward(self, x_scalar, x_vector, edge_index, edge_attr, pos):
        row, col = edge_index

        # Calculate distances (invariant)
        dist = torch.norm(pos[row] - pos[col], dim=-1, keepdim=True)

        # Calculate unit vectors (equivariant)
        edge_vec = pos[row] - pos[col]

        # Apply radial basis functions (invariant)
        rbf_output = self.rbf(dist)

        # Process scalar features (invariant path)
        scalar_message_input = torch.cat([
            x_scalar[row],
            x_scalar[col],
            rbf_output
        ], dim=-1)

        scalar_message = self.scalar_mlp(scalar_message_input)

        # Compute spherical harmonics (equivariant)
        edge_sh = spherical_harmonics(
            list(range(self.lmax + 1)),
            edge_vec / (dist + 1e-8),
            normalize=True
        )

        # Combine scalar and vector features for equivariant processing
        src_features = torch.cat([x_scalar[row], x_vector[row].reshape(x_vector[row].shape[0], -1)], dim=-1)

        # Apply tensor product for equivariant message passing
        message = self.tp(src_features, edge_sh)

        # Aggregate messages (sum is equivariant)
        scalar_out = scatter(scalar_message, col, dim=0, dim_size=x_scalar.size(0), reduce='add')
        vector_out = scatter(message[:, self.channels:].view(-1, self.channels, 3), col, dim=0, dim_size=x_vector.size(0), reduce='add')

        # Apply activations manually
        scalar_out = self.scalar_act(scalar_out)
        gates = self.gate_act(scalar_out)
        gated_vectors = vector_out * gates.unsqueeze(-1)

        x_scalar_new = x_scalar + scalar_out
        x_vector_new = x_vector + gated_vectors

        return x_scalar_new, x_vector_new

class RadialBasisLayer(nn.Module):
    def __init__(self, num_rbf, cutoff):
        super().__init__()
        self.num_rbf = num_rbf
        self.cutoff = cutoff


        self.centers = nn.Parameter(torch.linspace(0, cutoff, num_rbf), requires_grad=False)
        self.widths = nn.Parameter((cutoff / num_rbf) * torch.ones(num_rbf), requires_grad=False)

    def forward(self, dist):
        # Apply cutoff
        dist = dist.clamp(max=self.cutoff)


        dist_expanded = dist.expand(-1, self.num_rbf)
        rbf = torch.exp(-((dist_expanded - self.centers.view(1, -1)) / self.widths.view(1, -1))**2)

        envelope = self._envelope(dist)

        envelope_expanded = envelope.expand_as(rbf)
        return rbf * envelope_expanded

    def _envelope(self, dist):

        return 1 - (dist / self.cutoff)**2


class E3EquivariantPrimaryCapsuleLayer(nn.Module):
    def __init__(self, scalar_features, vector_features, out_caps, caps_dim):
        super().__init__()
        self.out_caps = out_caps
        self.caps_dim = caps_dim

        # Scalar projection (invariant)
        self.scalar_projection = nn.Linear(scalar_features, out_caps * (caps_dim // 2))

        # Vector projection weights (invariant)
        self.vector_weights = nn.Linear(scalar_features, out_caps)

        # Equivariant vector transformation
        irreps_in1 = o3.Irreps(f"{vector_features}x1e")  # Input vectors
        irreps_in2 = o3.Irreps("0e")  # Scalar input for invariant output
        irreps_out = o3.Irreps(f"{caps_dim//2}x0e")  # Output scalars (invariants from vectors)

        # Use tensor product to create invariants from vectors
        self.vector_tp = FullyConnectedTensorProduct(
            irreps_in1=irreps_in1,
            irreps_in2=irreps_in2,
            irreps_out=irreps_out,
            internal_weights=True
        )

    def forward(self, x_scalar, x_vector):
        batch_size = x_scalar.size(0)

        # Project scalar features (invariant)
        scalar_out = self.scalar_projection(x_scalar)
        scalar_out = scalar_out.view(batch_size, self.out_caps, self.caps_dim // 2)

        # Get weights for vector features (invariant)
        vector_weights = self.vector_weights(x_scalar)
        vector_weights = vector_weights.view(batch_size, self.out_caps, 1)

        # Create equivariant capsules
        vector_caps = []

        # Process vectors equivariantly for each capsule
        for i in range(self.out_caps):
            # Weight vectors (equivariant operation)
            weighted_vectors = vector_weights[:, i:i+1, :] * x_vector

            # Create invariants from vectors using tensor product
            # Reshape for tensor product
            weighted_vectors_flat = weighted_vectors.reshape(batch_size, -1)

            # Apply tensor product to get invariants
            invariants = self.vector_tp(weighted_vectors_flat, torch.ones(batch_size, 1, device=x_scalar.device))

            # Reshape to capsule dimension
            invariants = invariants.view(batch_size, self.caps_dim // 2)
            vector_caps.append(invariants)

        # Stack to get [batch_size, out_caps, caps_dim//2]
        vector_caps = torch.stack(vector_caps, dim=1)

        # Concatenate scalar and vector parts
        capsules = torch.cat([scalar_out, vector_caps], dim=2)

        return capsules, x_vector

class E3EquivariantSecondaryCapsuleLayer(nn.Module):
    def __init__(self, in_dim, out_caps, out_dim, routing_iterations=2):
        super().__init__()
        self.out_caps = out_caps
        self.out_dim = out_dim
        self.routing_iterations = routing_iterations
        self.scalar_dim = out_dim // 2
        self.vector_dim = out_dim // 2
        self.in_dim = in_dim

        # Transform matrices for scalar parts (invariant)
        self.W_scalar = nn.Parameter(torch.randn(out_caps, in_dim // 2, self.scalar_dim))

        # Transform matrices for vector parts (invariant)
        self.W_vector = nn.Parameter(torch.randn(out_caps, in_dim // 2, self.vector_dim))

        self.bias = nn.Parameter(torch.zeros(out_caps, out_dim))

    def squash(self, tensor, dim=-1):
        # Squash is equivariant when applied to scalars
        squared_norm = (tensor ** 2).sum(dim=dim, keepdim=True)
        scale = squared_norm / (1 + squared_norm)
        return scale * tensor / (torch.sqrt(squared_norm) + 1e-8)

    def forward(self, x, x_vectors, batch):
        batch_size = batch.max().item() + 1
        secondary_capsules = []
        secondary_vectors = []

        for b in range(batch_size):
            mask = (batch == b)
            if mask.sum() == 0:
                continue

            x_b = x[mask]  # [nodes, primary_caps, primary_dim]
            x_vectors_b = x_vectors[mask] if x_vectors is not None else None

            # Split input into scalar and vector parts
            nodes, primary_caps, primary_dim = x_b.size()
            scalar_part = x_b[:, :, :primary_dim//2]  # [nodes, primary_caps, primary_dim//2]
            vector_part = x_b[:, :, primary_dim//2:]  # [nodes, primary_caps, primary_dim//2]

            # Prepare prediction vectors for each primary capsule to each secondary capsule
            u_hat = torch.zeros(nodes, primary_caps, self.out_caps, self.out_dim, device=x.device)

            # For each output capsule
            for i in range(self.out_caps):
                # Transform scalar part (invariant)
                scalar_transformed = torch.matmul(scalar_part, self.W_scalar[i])  # [nodes, primary_caps, scalar_dim]
                # Transform vector part (invariant)
                vector_transformed = torch.matmul(vector_part, self.W_vector[i])  # [nodes, primary_caps, vector_dim]
                # Combine
                u_hat[:, :, i, :self.scalar_dim] = scalar_transformed
                u_hat[:, :, i, self.scalar_dim:] = vector_transformed

            # Flatten first two dimensions for routing
            u_hat_flat = u_hat.view(-1, self.out_caps, self.out_dim)  # [nodes*primary_caps, out_caps, out_dim]
            num_inputs = u_hat_flat.size(0)  # nodes*primary_caps

            # Initialize routing logits (invariant)
            b_ij = torch.zeros(num_inputs, self.out_caps, device=x.device)

            # Routing algorithm (invariant)
            for _ in range(self.routing_iterations):
                c_ij = F.softmax(b_ij, dim=1)  # [nodes*primary_caps, out_caps]
                c_ij = c_ij.unsqueeze(2)
                s_j = (c_ij * u_hat_flat).sum(dim=0) + self.bias
                v_j = self.squash(s_j, dim=1)
                if _ < self.routing_iterations - 1:
                    u_scalar = u_hat_flat[:, :, :self.scalar_dim]  # [num_inputs, out_caps, scalar_dim]
                    v_scalar = v_j[:, :self.scalar_dim].unsqueeze(0)  # [1, out_caps, scalar_dim]

                    # Calculate agreement as sum of element-wise products (invariant)
                    agreement = (u_scalar * v_scalar).sum(dim=2)  # [num_inputs, out_caps]
                    b_ij = b_ij + agreement

            # Process equivariant vectors if provided
            if x_vectors_b is not None:
                # Reshape routing coefficients
                c_ij_reshaped = c_ij.view(nodes, primary_caps, self.out_caps, 1)

                # Get vector dimensions
                vector_channels = x_vectors_b.size(2)

                # Initialize routed vectors with correct dimensions
                routed_vectors = torch.zeros(self.out_caps, vector_channels, 3, device=x.device)

                # Vectorized aggregation (equivariant)
                for k in range(self.out_caps):
                    # Sum weighted vectors for this output capsule (equivariant)
                    weights = c_ij_reshaped[:, :, k, 0].view(nodes, primary_caps, 1, 1)

                    # Make sure dimensions match before summing
                    if x_vectors_b.dim() == 3:  # [nodes, channels, 3]
                        # Expand to match primary_caps dimension
                        expanded_vectors = x_vectors_b.unsqueeze(1).expand(-1, primary_caps, -1, -1)
                        routed_vectors[k] = (weights * expanded_vectors).sum(dim=(0, 1))
                    else:  # [nodes, primary_caps, channels, 3]
                        routed_vectors[k] = (weights * x_vectors_b).sum(dim=(0, 1))

                secondary_vectors.append(routed_vectors.unsqueeze(0))

            secondary_capsules.append(v_j.unsqueeze(0))

        if not secondary_capsules:
            return torch.zeros((batch_size, self.out_caps, self.out_dim), device=x.device), None

        return torch.cat(secondary_capsules, dim=0), torch.cat(secondary_vectors, dim=0) if secondary_vectors else None


class E3LayerNorm(nn.Module):
    """E(3)-equivariant layer normalization"""
    def __init__(self, channels, scalar_only=False):
        super().__init__()
        self.channels = channels
        self.scalar_only = scalar_only

        # Learnable parameters for scalar normalization
        self.scalar_scale = nn.Parameter(torch.ones(channels))
        self.scalar_bias = nn.Parameter(torch.zeros(channels))

        if not scalar_only:
            # For vector normalization (only scale, no bias to preserve equivariance)
            self.vector_scale = nn.Parameter(torch.ones(channels))

    def forward(self, x_scalar, x_vector=None):
        # Normalize scalar features (invariant)
        mean = x_scalar.mean(dim=1, keepdim=True)
        var = x_scalar.var(dim=1, keepdim=True, unbiased=False)
        x_scalar = (x_scalar - mean) / (var + 1e-5).sqrt()
        x_scalar = x_scalar * self.scalar_scale.view(1, -1) + self.scalar_bias.view(1, -1)

        # Normalize vector features if provided (equivariant)
        if x_vector is not None and not self.scalar_only:
            # Compute vector norms (invariant)
            vec_norm = torch.norm(x_vector, dim=2, keepdim=True)
            vec_mean = vec_norm.mean(dim=1, keepdim=True)
            vec_var = vec_norm.var(dim=1, keepdim=True, unbiased=False)

            # Scale vectors by normalized norms (equivariant)
            scale = (vec_norm / (vec_var + 1e-5).sqrt()) * self.vector_scale.view(1, -1, 1)
            x_vector = x_vector * scale

        return x_scalar, x_vector

class E3EquivariantCrystalGNNCapsNet(nn.Module):
    def __init__(self, node_features, edge_features, hidden_channels,
                 num_conv_layers, primary_caps, primary_dim,
                 secondary_caps, secondary_dim, dropout_rate=0.0):
        super().__init__()

        # Node embedding - invariant
        self.node_embedding = nn.Sequential(
            nn.Linear(node_features, hidden_channels),
            nn.BatchNorm1d(hidden_channels),
            nn.ReLU(),
            nn.Linear(hidden_channels, hidden_channels),
            nn.BatchNorm1d(hidden_channels),
            nn.ReLU(),
        )

        # Edge embedding - invariant
        self.edge_embedding = nn.Sequential(
            nn.Linear(edge_features, hidden_channels),
            nn.ReLU(),
            nn.Linear(hidden_channels, hidden_channels),
            nn.ReLU()
        )

        # Initialize vector features -equivariant
        self.init_vector_features = nn.Linear(hidden_channels, hidden_channels)

        # E(3)-equivariant convolution layers
        self.convs = nn.ModuleList([
            E3EquivariantCGCNNConv(hidden_channels)
            for _ in range(num_conv_layers)
        ])

        # Layer normalization - equivariant
        self.layer_norms = nn.ModuleList([
            E3LayerNorm(hidden_channels)
            for _ in range(num_conv_layers)
        ])

        # Capsule layers-equivariant
        self.primary_caps = E3EquivariantPrimaryCapsuleLayer(
            scalar_features=hidden_channels,
            vector_features=hidden_channels,
            out_caps=primary_caps,
            caps_dim=primary_dim
        )

        self.secondary_caps = E3EquivariantSecondaryCapsuleLayer(
            in_dim=primary_dim,
            out_caps=secondary_caps,
            out_dim=secondary_dim,
            routing_iterations=2
        )

        # Attention network - invariant
        self.attn_net = nn.Sequential(
            nn.Linear(secondary_dim // 2, 1)
        )

        # Final predictor (invariant)
        self.predictor = nn.Sequential(
            nn.Linear(secondary_dim, hidden_channels//2),
            nn.BatchNorm1d(hidden_channels//2),
            nn.ReLU(),
            nn.Linear(hidden_channels//2, hidden_channels//4),
            nn.BatchNorm1d(hidden_channels//4),
            nn.ReLU(),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_channels//4, 1)
        )

        self.dropout = nn.Dropout(dropout_rate)

        # Scalar activation for gating
        self.scalar_act = nn.ReLU()
        self.gate_act = nn.Sigmoid()

    def forward(self, data):
        x, edge_index, edge_attr, pos = data.x, data.edge_index, data.edge_attr, data.pos
        batch = data.batch if hasattr(data, 'batch') else torch.zeros(x.size(0), dtype=torch.long, device=x.device)

        # Process node features (invariant)
        x_scalar = self.node_embedding(x)
        edge_attr = self.edge_embedding(edge_attr)

        # Initialize vector features (equivariant)
        vector_weights = self.init_vector_features(x_scalar)
        # Create vector features with shape [batch_size, hidden_channels, 3]
        x_vector = torch.zeros(x_scalar.size(0), vector_weights.size(1), 3, device=x_scalar.device)
        # Use position information to initialize vectors
        for i in range(x_scalar.size(0)):
            x_vector[i] = vector_weights[i].unsqueeze(-1) * pos[i].unsqueeze(0)

        # Apply E(3)-equivariant convolutions
        for i, (conv, norm) in enumerate(zip(self.convs, self.layer_norms)):
            x_scalar_res = x_scalar
            x_vector_res = x_vector

            x_scalar, x_vector = conv(x_scalar, x_vector, edge_index, edge_attr, pos)

            x_scalar, x_vector = norm(x_scalar, x_vector)
            x_scalar_act = self.scalar_act(x_scalar)

            # Create gates from scalar features
            gates = self.gate_act(x_scalar)

            # Apply gates to vector features (equivariant operation)
            x_vector = x_vector * gates.unsqueeze(-1)

            # Residual connections
            if i > 0:
                x_scalar = x_scalar_act + x_scalar_res
                x_vector = x_vector + x_vector_res
            else:
                x_scalar = x_scalar_act

        # Apply capsule layers (equivariant)
        primary_caps, primary_vectors = self.primary_caps(x_scalar, x_vector)

        # Ensure primary_vectors has the right shape for secondary_caps
        if primary_vectors is not None:
            # Get the shape of primary_vectors
            if primary_vectors.dim() == 3:  # [batch_size, vector_features, 3]
                # Expand to match primary_caps dimension
                primary_caps_size = primary_caps.size(1)
                primary_vectors = primary_vectors.unsqueeze(1).expand(-1, primary_caps_size, -1, -1)

        # Apply secondary capsule layer
        secondary_caps, _ = self.secondary_caps(primary_caps, primary_vectors, batch)

        # Use only scalar part for attention
        scalar_part = secondary_caps[:, :, :secondary_caps.size(2)//2]
        attn_weights = F.softmax(self.attn_net(scalar_part), dim=1)

        # Weight capsules by attention
        weighted_caps = (attn_weights * secondary_caps).sum(dim=1)

        # Final prediction
        return self.predictor(weighted_caps)

def train_spatial_gnn(
    model, train_loader, optimizer, device,
    epochs=100, scheduler=None, early_stopping_patience=30,
    checkpoint_path="best_model.pt", metrics_path="training_metrics.csv"
):
    def run_caps_epoch(model, loader, optimizer=None):
        is_train = optimizer is not None
        model.train() if is_train else model.eval()
        total_loss, total_mae = 0.0, 0.0

        for batch in loader:
            batch = batch.to(device)
            if is_train:
                optimizer.zero_grad()

            output = model(batch)

            loss = F.mse_loss(output, batch.y)
            mae = F.l1_loss(output, batch.y)

            if is_train:
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 0.5)
                optimizer.step()

            total_loss += loss.item() * batch.num_graphs
            total_mae += mae.item() * batch.num_graphs

        size = len(loader.dataset)
        return total_loss / size, total_mae / size


    dataset = train_loader.dataset
    train_size = int(0.9 * len(dataset))
    val_size = len(dataset) - train_size
    train_data, val_data = random_split(dataset, [train_size, val_size], generator=torch.Generator().manual_seed(42))

    train_loader = DataLoader(train_data, batch_size=train_loader.batch_size, shuffle=True, collate_fn=Batch.from_data_list)
    val_loader = DataLoader(val_data, batch_size=train_loader.batch_size, collate_fn=Batch.from_data_list)

    print(f"CapsGNN: Training on {train_size}, validating on {val_size}")

    best_loss = float('inf')
    patience_counter = 0
    train_losses, val_losses = [], []

    metrics = {
        'epoch': [], 'train_loss': [], 'train_mae': [],
        'val_loss': [], 'val_mae': [], 'learning_rate': []
    }
    pd.DataFrame(columns=metrics.keys()).to_csv(metrics_path, index=False)

    for epoch in range(epochs):
        train_loss, train_mae = run_caps_epoch(model, train_loader, optimizer)
        val_loss, val_mae = run_caps_epoch(model, val_loader)

        lr = optimizer.param_groups[0]['lr']
        train_losses.append(train_loss)
        val_losses.append(val_loss)

        metrics['epoch'].append(epoch + 1)
        metrics['train_loss'].append(train_loss)
        metrics['train_mae'].append(train_mae)
        metrics['val_loss'].append(val_loss)
        metrics['val_mae'].append(val_mae)
        metrics['learning_rate'].append(lr)

        pd.DataFrame({k: [v[-1]] for k, v in metrics.items()}).to_csv(
            metrics_path, mode='a', header=False, index=False
        )

        if scheduler:
            scheduler.step(val_loss)

        print(f"[Epoch {epoch+1}] Train Loss: {train_loss:.4f}, MAE: {train_mae:.4f} | "
              f"Val Loss: {val_loss:.4f}, MAE: {val_mae:.4f}, LR: {lr:.2e}")

        if val_loss < best_loss:
            best_loss = val_loss
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': best_loss,
            }, checkpoint_path)
            print(f"Saved best model at epoch {epoch+1}")
        else:
            patience_counter += 1
            if patience_counter >= early_stopping_patience:
                print(f"Early stopping at epoch {epoch+1}")
                break


    checkpoint = torch.load(checkpoint_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    print(f" Loaded best model from epoch {checkpoint['epoch']+1} with loss {checkpoint['loss']:.6f}")

    pd.DataFrame(metrics).to_csv(metrics_path, index=False)
    return model, train_losses, val_losses, metrics


def evaluate_spatial_gnn(model, test_loader, device, results_path=None):
    model.eval()
    total_mse = 0
    total_mae = 0
    predictions, targets, material_ids = [], [], []

    with torch.no_grad():
        for batch in test_loader:
            batch = batch.to(device)
            output = model(batch)
            target = batch.y

            total_mse += F.mse_loss(output, target, reduction='sum').item()
            total_mae += F.l1_loss(output, target, reduction='sum').item()

            predictions.extend(output.cpu().numpy().flatten())
            targets.extend(target.cpu().numpy().flatten())

            if hasattr(batch, 'material_id'):
                ids = batch.material_id if isinstance(batch.material_id, list) else [batch.material_id[i] for i in range(batch.num_graphs)]
                material_ids.extend(ids)

    num_samples = len(test_loader.dataset)
    mse = total_mse / num_samples
    mae = total_mae / num_samples
    rmse = np.sqrt(mse)
    r2 = r2_score(targets, predictions)

    print(f"\nTest Results:")
    print(f"  MSE  : {mse:.6f}")
    print(f"  RMSE : {rmse:.6f}")
    print(f"  MAE  : {mae:.6f}")
    print(f"  R²   : {r2:.6f}")

    results = {
        "mse": mse,
        "rmse": rmse,
        "mae": mae,
        "r2": r2,
        "predictions": predictions,
        "targets": targets,
        "material_ids": material_ids
    }

    if results_path:
        pd.DataFrame([{
            "mse": mse,
            "rmse": rmse,
            "mae": mae,
            "r2": r2
        }]).to_csv(results_path, index=False)
        print(f"Test metrics saved to {results_path}")

    return results

def run_spatial_gnn_capsnet(dataset_path, target_name, epochs, pretrained_model_path=None):
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


    target_name = str(target_name)
    os.makedirs(f"/content/gdrive/MyDrive/2024SUMMER/MaterialsProjectData/20KSPLIT/ReRESULTS/3ITERATIONS_{target_name}", exist_ok=True)


    metrics_path = os.path.join(f"/content/gdrive/MyDrive/2024SUMMER/MaterialsProjectData/20KSPLIT/ReRESULTS/3ITERATIONS_{target_name}", f"training_metrics_{target_name}.csv")
    checkpoint_path = os.path.join(f"/content/gdrive/MyDrive/2024SUMMER/MaterialsProjectData/20KSPLIT/ReRESULTS/3ITERATIONS_{target_name}", f"best_spatial_gnn_capsnet_{target_name}.pt")
    test_metrics_path = os.path.join(f"/content/gdrive/MyDrive/2024SUMMER/MaterialsProjectData/20KSPLIT/ReRESULTS/3ITERATIONS_{target_name}", f"test_metrics_{target_name}.csv")
    results_file = os.path.join(f"/content/gdrive/MyDrive/2024SUMMER/MaterialsProjectData/20KSPLIT/ReRESULTS/3ITERATIONS_{target_name}", f"spatial_gnn_capsnet_{target_name}_results.csv")

    dataset = CartesianGraphDataset(dataset_path, target_name=target_name)
    print(f"Dataset loaded with {len(dataset)} samples")


    train_size = int(0.8 * len(dataset))
    test_size = len(dataset) - train_size
    train_dataset, test_dataset = random_split(dataset, [train_size, test_size])
    print(f"Split: {train_size} train, {test_size} test")


    batch_size = 64
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=Batch.from_data_list)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, collate_fn=Batch.from_data_list)

    print("Initializing Spatial-Aware GNN + Capsule Network model")
    model = E3EquivariantCrystalGNNCapsNet(
        node_features=dataset[0].x.size(1),
        edge_features=dataset[0].edge_attr.size(1),
        hidden_channels=256,
        num_conv_layers=2,
        primary_caps=8,
        primary_dim=16,
        secondary_caps=6,
        secondary_dim=16,
        dropout_rate=0.01
    ).to(device)


    if pretrained_model_path and os.path.exists(pretrained_model_path):
        print(f"Loading pretrained model from {pretrained_model_path}")
        checkpoint = torch.load(pretrained_model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print("Pretrained model loaded.")
    else:
        print("No pretrained model provided. Training from scratch.")


    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {total_params:,} total, {trainable_params:,} trainable")

    for m in model.modules():
        if isinstance(m, nn.Linear):
            nn.init.xavier_uniform_(m.weight)
            if m.bias is not None:
                nn.init.zeros_(m.bias)


    optimizer = torch.optim.AdamW(model.parameters(), lr=5e-3, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=15, verbose=True, min_lr=1e-6
    )

    print(f"Training for {epochs} epochs...")
    model, train_losses, val_losses, _ = train_spatial_gnn(
        model, train_loader, optimizer, device,
        epochs=epochs,
        scheduler=scheduler,
        early_stopping_patience=50,
        checkpoint_path=checkpoint_path,
        metrics_path=metrics_path
    )


    print("Evaluating on test set...")
    test_results = evaluate_spatial_gnn(model, test_loader, device, results_path=test_metrics_path)

    results_df = pd.DataFrame({
        'material_id': test_results['material_ids'],
        'true_value': test_results['targets'],
        'predicted_value': test_results['predictions'],
        'absolute_error': np.abs(np.array(test_results['predictions']) - np.array(test_results['targets']))
    })
    results_df.to_csv(results_file, index=False)
    print(f"Prediction results saved to {results_file}")

    return model, test_results


if __name__ == "__main__":
    dataset_path = "/content/gdrive/MyDrive/2024SUMMER/MaterialsProjectData/MaterialsProjectData/20KSPLIT/"
    #pretrained_model_path = "/content/gdrive/MyDrive/2024SUMMER/MaterialsProjectData/20KSPLIT/4SAGCNresults_e_form/best_spatial_gnn_capsnet_e_form.pt"
    pretrained_model_path = None

    #if pretrained model exists
    '''if not os.path.exists(pretrained_model_path):
        print(f"Warning: Pretrained model not found at {pretrained_model_path}")
        pretrained_model_path = None'''

    model, results = run_spatial_gnn_capsnet(
        dataset_path,
        target_name="e_form",
        epochs=200,
        pretrained_model_path=pretrained_model_path
    )

    print("\nTest Results:")
    print(f"MSE: {results['mse']:.4f}")
    print(f"MAE: {results['mae']:.4f}")
    print(f"R²: {results['r2']:.4f}")
