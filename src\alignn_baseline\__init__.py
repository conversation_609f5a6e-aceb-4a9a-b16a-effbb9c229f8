"""
ALIGNN Baseline Implementation for CGN-E3 Comparison

This module provides an ALIGNN (Atomistic Line Graph Neural Network) implementation
that is compatible with the CGN-E3 dataset format for performance comparison.

Based on: https://github.com/usnistgov/alignn
Paper: https://www.nature.com/articles/s41524-021-00650-1
"""

__version__ = "0.1.0"
__author__ = "ALIGNN Baseline Team"

from .data import ALIGNNDataset, create_line_graph
from .models import ALIGNN, ALIGNNConv, EdgeGatedGraphConv
from .utils import train_alignn, evaluate_alignn, run_alignn_pipeline

__all__ = [
    "ALIGNNDataset",
    "create_line_graph", 
    "ALIGNN",
    "ALIGNNConv",
    "EdgeGatedGraphConv",
    "train_alignn",
    "evaluate_alignn", 
    "run_alignn_pipeline"
]
