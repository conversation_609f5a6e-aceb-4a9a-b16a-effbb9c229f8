#!/usr/bin/env python3
"""
Main script for training and evaluating ALIGNN baseline model.

This script provides a command-line interface for training ALIGNN models
on crystal property prediction tasks using the same dataset format as CGN-E3.
"""

import argparse
import json
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from alignn_baseline.utils.pipeline import run_alignn_pipeline


def main():
    parser = argparse.ArgumentParser(
        description="Train and evaluate ALIGNN baseline model for crystal property prediction"
    )
    
    # Required arguments
    parser.add_argument(
        "--dataset_path",
        type=str,
        required=True,
        help="Path to dataset directory containing BandgapTargets files"
    )
    
    parser.add_argument(
        "--target_name", 
        type=str,
        required=True,
        help="Name of target property to predict (e.g., 'e_form', 'band_gap')"
    )
    
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="Directory to save model and results"
    )
    
    # Optional arguments
    parser.add_argument(
        "--config",
        type=str,
        default="config/alignn_config.json",
        help="Path to configuration file (default: config/alignn_config.json)"
    )
    
    parser.add_argument(
        "--device",
        type=str,
        default="auto",
        choices=["auto", "cpu", "cuda"],
        help="Device to use for training (default: auto)"
    )
    
    parser.add_argument(
        "--epochs",
        type=int,
        help="Number of training epochs (overrides config)"
    )
    
    parser.add_argument(
        "--batch_size",
        type=int,
        help="Batch size for training (overrides config)"
    )
    
    parser.add_argument(
        "--learning_rate",
        type=float,
        help="Learning rate for training (overrides config)"
    )
    
    parser.add_argument(
        "--hidden_channels",
        type=int,
        help="Number of hidden channels in model (overrides config)"
    )
    
    parser.add_argument(
        "--num_layers",
        type=int,
        help="Number of ALIGNN layers (overrides config)"
    )
    
    parser.add_argument(
        "--eval_only",
        action="store_true",
        help="Only evaluate existing model (requires trained model in output_dir)"
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not os.path.exists(args.dataset_path):
        print(f"Error: Dataset path '{args.dataset_path}' does not exist")
        sys.exit(1)
    
    # Load configuration
    config = {}
    if os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config = json.load(f)
        print(f"Loaded configuration from {args.config}")
    else:
        print(f"Warning: Configuration file '{args.config}' not found, using defaults")
    
    # Override config with command line arguments
    if args.epochs:
        config.setdefault('training', {})['num_epochs'] = args.epochs
    
    if args.batch_size:
        config.setdefault('training', {})['batch_size'] = args.batch_size
    
    if args.learning_rate:
        config.setdefault('training', {})['learning_rate'] = args.learning_rate
    
    if args.hidden_channels:
        config.setdefault('model', {})['hidden_channels'] = args.hidden_channels
    
    if args.num_layers:
        config.setdefault('model', {})['num_layers'] = args.num_layers
    
    # Print configuration
    print("\nConfiguration:")
    print(json.dumps(config, indent=2))
    print()
    
    # Check if evaluation only
    if args.eval_only:
        model_path = os.path.join(args.output_dir, 'best_model.pt')
        if not os.path.exists(model_path):
            print(f"Error: No trained model found at {model_path}")
            sys.exit(1)
        
        print("Evaluation-only mode not implemented yet")
        sys.exit(1)
    
    # Run pipeline
    try:
        print(f"Starting ALIGNN training pipeline...")
        print(f"Dataset: {args.dataset_path}")
        print(f"Target: {args.target_name}")
        print(f"Output: {args.output_dir}")
        print(f"Device: {args.device}")
        print()
        
        results = run_alignn_pipeline(
            dataset_path=args.dataset_path,
            target_name=args.target_name,
            output_dir=args.output_dir,
            config=config,
            device=args.device
        )
        
        print("\n" + "="*60)
        print("ALIGNN TRAINING COMPLETED SUCCESSFULLY")
        print("="*60)
        
        # Print key results
        test_metrics = results['test_metrics']
        print(f"\nFinal Test Results:")
        print(f"  MAE: {test_metrics['mae']:.4f}")
        print(f"  RMSE: {test_metrics['rmse']:.4f}")
        print(f"  R²: {test_metrics['r2']:.4f}")
        print(f"  MAPE: {test_metrics['mape']:.2f}%")
        
        print(f"\nModel Information:")
        print(f"  Parameters: {results['model_info']['num_parameters']:,}")
        print(f"  Node Features: {results['model_info']['node_features']}")
        print(f"  Edge Features: {results['model_info']['edge_features']}")
        
        print(f"\nResults saved to: {args.output_dir}")
        print("  - best_model.pt: Trained model checkpoint")
        print("  - results.json: Detailed results and metrics")
        print("  - predictions.json: Test set predictions")
        print("  - predictions_plot.png: Prediction visualization")
        print("  - performance_report.txt: Performance analysis")
        print("  - training.log: Training logs")
        
    except Exception as e:
        print(f"\nError during training: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
