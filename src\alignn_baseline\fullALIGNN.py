import os
import json
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset
from torch_geometric.data import Data

import sys
from pathlib import Path



class ALIGNNDataset(Dataset):
  

    def __init__(self, path, target_name, cutoff=8.0, add_self_loops=True):
        super().__init__()
        self.path = path
        self.target_name = target_name
        self.cutoff = cutoff
        self.add_self_loops = add_self_loops

        # Load all required data (same as CGN-E3)
        self._load_graph_data()
        self._load_config()
        self._load_targets()

        # Validate data consistency
        if len(self.graph_data) != len(self.targets):
            raise ValueError(
                f"Graph count ({len(self.graph_data)}) doesn't match "
                f"target count ({len(self.targets)})"
            )

    def _load_graph_data(self):
        """Load and validate graph data from NPZ file."""
        npz_path = os.path.join(self.path, "BandgapTargets.npz")
        try:
            with np.load(npz_path, allow_pickle=True) as data:
                graph_dict = data['graph_dict'].item()
                self.graph_names = list(graph_dict.keys())
                self.graph_data = []

                for name, graph in graph_dict.items():
                    try:
                        if 'cart_coords' not in graph:
                            raise ValueError(f"Missing cart_coords in graph {name}")
                        self.graph_data.append(Graph(graph))
                    except ValueError as e:
                        print(f"Skipping invalid graph {name}: {str(e)}")
                        continue

                if not self.graph_data:
                    raise ValueError("No valid graphs found in NPZ file")

        except Exception as e:
            raise RuntimeError(f"Error loading graph data: {str(e)}")

    def _load_config(self):
        """Load and validate configuration."""
        config_path = os.path.join(self.path, "BandgapTargets_config.json")
        try:
            with open(config_path) as f:
                config = json.load(f)

            self.atomic_numbers = config["atomic_numbers"]
            self.node_vectors = np.array(config["node_vectors"])
            self.n_node_feat = len(self.node_vectors[0])
            self.pos_dim = config.get("pos_dim", 3)

            # Create atomic number to index mapping
            self.atomic_to_idx = {
                num: idx for idx, num in enumerate(self.atomic_numbers)
            }

            if len(self.atomic_to_idx) != len(self.atomic_numbers):
                raise ValueError("Duplicate atomic numbers in config")

        except Exception as e:
            raise RuntimeError(f"Error loading config: {str(e)}")

    def _load_targets(self):
        """Load target values from CSV file."""
        targets_path = os.path.join(self.path, "BandgapTargets.csv")
        try:
            df = pd.read_csv(targets_path)

            if self.target_name not in df.columns:
                raise ValueError(
                    f"Target column '{self.target_name}' not found in CSV"
                )

            self.targets = df[self.target_name].values
            if len(self.targets) == 0:
                raise ValueError("No targets found in CSV file")

            # Update graph names from CSV (more reliable than NPZ keys)
            self.graph_names = df['mpid'].values.tolist()

        except Exception as e:
            raise RuntimeError(f"Error loading targets: {str(e)}")

    def __len__(self):
        """Return the number of samples in the dataset."""
        return len(self.graph_data)

    def __getitem__(self, index):
      
        graph = self.graph_data[index]

        # Create node features from atomic numbers (same as CGN-E3)
        node_features = np.zeros((len(graph.nodes), self.n_node_feat))
        for i, atomic_num in enumerate(graph.nodes):
            idx = self.atomic_to_idx[atomic_num]
            node_features[i] = self.node_vectors[idx]

        # Handle position data (ensure it's a tensor)
        if isinstance(graph.cart_coords, torch.Tensor):
            pos = graph.cart_coords.clone().detach()
        else:
            pos = torch.tensor(graph.cart_coords, dtype=torch.float32)

        # Create original atomic graph
        atomic_graph = Data(
            x=torch.tensor(node_features, dtype=torch.float32),
            edge_index=graph.edge_index,
            edge_attr=graph.edge_attr,
            pos=pos,
            num_nodes=len(graph.nodes)
        )

        # Create line graph for ALIGNN
        _, line_graph = create_line_graph(atomic_graph, cutoff=self.cutoff)

        # Add self-loops to line graph if requested
        if self.add_self_loops:
            line_graph = add_self_loops_to_line_graph(line_graph)

        # Prepare target
        target = torch.tensor([self.targets[index]], dtype=torch.float32)

        return {
            'graph': atomic_graph,
            'line_graph': line_graph,
            'target': target,
            'material_id': self.graph_names[index]
        }

    def get_statistics(self):
        """
        Get dataset statistics for normalization.

        Returns:
            dict: Statistics including mean, std, min, max of targets
        """
        targets = np.array(self.targets)
        return {
            'target_mean': float(np.mean(targets)),
            'target_std': float(np.std(targets)),
            'target_min': float(np.min(targets)),
            'target_max': float(np.max(targets)),
            'num_samples': len(targets),
            'num_node_features': self.n_node_feat,
            'atomic_numbers': self.atomic_numbers
        }

    def get_sample_graphs(self, num_samples=5):
        """
        Get sample graphs for analysis.

        Args:
            num_samples (int): Number of sample graphs to return

        Returns:
            list: List of sample graph dictionaries
        """
        samples = []
        indices = np.random.choice(len(self), min(num_samples, len(self)), replace=False)

        for idx in indices:
            sample = self[idx]
            graph_info = {
                'material_id': sample['material_id'],
                'num_atoms': sample['graph'].num_nodes,
                'num_bonds': sample['graph'].edge_index.size(1),
                'num_line_nodes': sample['line_graph'].num_nodes,
                'num_line_edges': sample['line_graph'].edge_index.size(1),
                'target': sample['target'].item()
            }
            samples.append(graph_info)

        return samples
import torch
import numpy as np
from torch_geometric.data import Data
from typing import Tuple, Optional


def create_line_graph(data: Data, cutoff: float = 8.0) -> Tuple[Data, Data]:
    """
    Create line graph from atomic graph for ALIGNN model.

    The line graph L(G) of a graph G has:
    - Nodes corresponding to edges in G (representing bonds)
    - Edges connecting nodes that share a common vertex in G (representing angles)

    Args:
        data: PyTorch Geometric Data object with atomic graph
        cutoff: Distance cutoff for creating additional edges

    Returns:
        tuple: (original_graph, line_graph)
    """
    # Extract basic information
    pos = data.pos
    edge_index = data.edge_index
    num_nodes = data.x.size(0)
    num_edges = edge_index.size(1)

    # Create line graph nodes (one for each edge in original graph)
    # Line graph node features are edge features from original graph
    if hasattr(data, 'edge_attr') and data.edge_attr is not None:
        line_node_features = data.edge_attr
    else:
        # If no edge attributes, create basic features from distances
        edge_vectors = pos[edge_index[1]] - pos[edge_index[0]]
        edge_distances = torch.norm(edge_vectors, dim=1, keepdim=True)
        line_node_features = edge_distances

    # Create line graph edges (connect edges that share a vertex)
    line_edge_sources = []
    line_edge_targets = []
    line_edge_features = []

    # For each node in the original graph, find all edges connected to it
    for node_idx in range(num_nodes):
        # Find all edges connected to this node
        connected_edges = []
        for edge_idx in range(num_edges):
            if edge_index[0, edge_idx] == node_idx or edge_index[1, edge_idx] == node_idx:
                connected_edges.append(edge_idx)

        # Connect all pairs of edges that share this node
        for i, edge_i in enumerate(connected_edges):
            for j, edge_j in enumerate(connected_edges):
                if i != j:  # Don't connect edge to itself
                    line_edge_sources.append(edge_i)
                    line_edge_targets.append(edge_j)

                    # Create edge feature for line graph edge (angle information)
                    # Get the three atoms involved in this angle
                    edge_i_atoms = [edge_index[0, edge_i].item(), edge_index[1, edge_i].item()]
                    edge_j_atoms = [edge_index[0, edge_j].item(), edge_index[1, edge_j].item()]

                    # Find the shared atom (center of angle)
                    shared_atom = None
                    for atom in edge_i_atoms:
                        if atom in edge_j_atoms:
                            shared_atom = atom
                            break

                    if shared_atom is not None:
                        # Get the other two atoms
                        atom_i = edge_i_atoms[0] if edge_i_atoms[1] == shared_atom else edge_i_atoms[1]
                        atom_j = edge_j_atoms[0] if edge_j_atoms[1] == shared_atom else edge_j_atoms[1]

                        # Calculate angle features
                        vec_i = pos[atom_i] - pos[shared_atom]
                        vec_j = pos[atom_j] - pos[shared_atom]

                        # Cosine of angle
                        cos_angle = torch.dot(vec_i, vec_j) / (torch.norm(vec_i) * torch.norm(vec_j) + 1e-8)

                        # Distance between the two outer atoms
                        distance_ij = torch.norm(pos[atom_i] - pos[atom_j])

                        # Bond lengths
                        bond_i_length = torch.norm(vec_i)
                        bond_j_length = torch.norm(vec_j)

                        line_edge_features.append([
                            cos_angle.item(),
                            distance_ij.item(),
                            bond_i_length.item(),
                            bond_j_length.item()
                        ])
                    else:
                        # Fallback if no shared atom found
                        line_edge_features.append([0.0, 0.0, 0.0, 0.0])

    # Convert to tensors
    if line_edge_sources:
        line_edge_index = torch.tensor([line_edge_sources, line_edge_targets], dtype=torch.long)
        line_edge_attr = torch.tensor(line_edge_features, dtype=torch.float32)
    else:
        # Handle case with no line graph edges
        line_edge_index = torch.empty((2, 0), dtype=torch.long)
        line_edge_attr = torch.empty((0, 4), dtype=torch.float32)

    # Create line graph data object
    line_graph = Data(
        x=line_node_features,
        edge_index=line_edge_index,
        edge_attr=line_edge_attr,
        num_nodes=num_edges
    )

    # Return both original graph and line graph
    return data, line_graph


def add_self_loops_to_line_graph(line_graph: Data) -> Data:
    """
    Add self-loops to line graph for better message passing.

    Args:
        line_graph: Line graph data object

    Returns:
        Line graph with self-loops added
    """
    num_nodes = line_graph.x.size(0)

    if num_nodes == 0:
        return line_graph

    # Create self-loop edges
    self_loop_index = torch.arange(num_nodes, dtype=torch.long).repeat(2, 1)

    # Create self-loop features (identity features)
    self_loop_attr = torch.zeros(num_nodes, line_graph.edge_attr.size(1))
    self_loop_attr[:, 0] = 1.0  # Set first feature to 1 for self-loops

    # Concatenate with existing edges
    if line_graph.edge_index.size(1) > 0:
        edge_index = torch.cat([line_graph.edge_index, self_loop_index], dim=1)
        edge_attr = torch.cat([line_graph.edge_attr, self_loop_attr], dim=0)
    else:
        edge_index = self_loop_index
        edge_attr = self_loop_attr

    return Data(
        x=line_graph.x,
        edge_index=edge_index,
        edge_attr=edge_attr,
        num_nodes=num_nodes
    )


def compute_bond_angles(pos: torch.Tensor, edge_index: torch.Tensor) -> torch.Tensor:
    """
    Compute bond angles for all triplets of atoms.

    Args:
        pos: Atomic positions [num_atoms, 3]
        edge_index: Edge connectivity [2, num_edges]

    Returns:
        Bond angles tensor
    """
    angles = []
    num_edges = edge_index.size(1)

    # For each edge, find other edges that share a vertex
    for i in range(num_edges):
        edge_i = edge_index[:, i]
        for j in range(i + 1, num_edges):
            edge_j = edge_index[:, j]

            # Check if edges share a vertex
            shared_vertex = None
            if edge_i[0] == edge_j[0]:
                shared_vertex = edge_i[0]
                other_i, other_j = edge_i[1], edge_j[1]
            elif edge_i[0] == edge_j[1]:
                shared_vertex = edge_i[0]
                other_i, other_j = edge_i[1], edge_j[0]
            elif edge_i[1] == edge_j[0]:
                shared_vertex = edge_i[1]
                other_i, other_j = edge_i[0], edge_j[1]
            elif edge_i[1] == edge_j[1]:
                shared_vertex = edge_i[1]
                other_i, other_j = edge_i[0], edge_j[0]

            if shared_vertex is not None:
                # Calculate angle
                vec1 = pos[other_i] - pos[shared_vertex]
                vec2 = pos[other_j] - pos[shared_vertex]

                cos_angle = torch.dot(vec1, vec2) / (torch.norm(vec1) * torch.norm(vec2) + 1e-8)
                angle = torch.acos(torch.clamp(cos_angle, -1.0, 1.0))
                angles.append(angle)

    return torch.tensor(angles) if angles else torch.empty(0)

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import MessagePassing
from torch_geometric.utils import add_self_loops, degree, softmax
from torch_geometric.typing import Adj, OptTensor
from typing import Optional


class EdgeGatedGraphConv(MessagePassing):
    """
    Edge-gated graph convolution layer.

    This layer implements edge-gated message passing where edge features
    are used to gate the messages between nodes.

    Args:
        in_channels (int): Input node feature dimensions
        out_channels (int): Output node feature dimensions
        edge_dim (int): Edge feature dimensions
        dropout (float): Dropout rate
        bias (bool): Whether to use bias
    """

    def __init__(self, in_channels: int, out_channels: int, edge_dim: int,
                 dropout: float = 0.0, bias: bool = True):
        super().__init__(aggr='add', node_dim=0)

        self.in_channels = in_channels
        self.out_channels = out_channels
        self.edge_dim = edge_dim
        self.dropout = dropout

        # Node transformation
        self.node_transform = nn.Linear(in_channels, out_channels, bias=bias)

        # Edge gating mechanism
        self.edge_gate = nn.Sequential(
            nn.Linear(edge_dim, out_channels),
            nn.Sigmoid()
        )

        # Message transformation
        self.message_transform = nn.Sequential(
            nn.Linear(in_channels + edge_dim, out_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # Self-loop transformation
        self.self_transform = nn.Linear(in_channels, out_channels, bias=bias)

        self.reset_parameters()

    def reset_parameters(self):
        """Reset layer parameters."""
        self.node_transform.reset_parameters()
        for layer in self.edge_gate:
            if hasattr(layer, 'reset_parameters'):
                layer.reset_parameters()
        for layer in self.message_transform:
            if hasattr(layer, 'reset_parameters'):
                layer.reset_parameters()
        self.self_transform.reset_parameters()

    def forward(self, x: torch.Tensor, edge_index: Adj,
                edge_attr: OptTensor = None) -> torch.Tensor:
        """
        Forward pass of edge-gated graph convolution.

        Args:
            x: Node features [num_nodes, in_channels]
            edge_index: Edge connectivity [2, num_edges]
            edge_attr: Edge features [num_edges, edge_dim]

        Returns:
            Updated node features [num_nodes, out_channels]
        """
        if edge_attr is None:
            raise ValueError("Edge attributes are required for EdgeGatedGraphConv")

        # Add self-loops
        edge_index, edge_attr = add_self_loops(
            edge_index, edge_attr, fill_value=0.0, num_nodes=x.size(0)
        )

        # Propagate messages
        out = self.propagate(edge_index, x=x, edge_attr=edge_attr)

        # Add self-connection
        out = out + self.self_transform(x)

        return out

    def message(self, x_j: torch.Tensor, edge_attr: torch.Tensor) -> torch.Tensor:
        """
        Create messages from source nodes to target nodes.

        Args:
            x_j: Source node features [num_edges, in_channels]
            edge_attr: Edge features [num_edges, edge_dim]

        Returns:
            Messages [num_edges, out_channels]
        """
        # Combine node and edge features
        combined = torch.cat([x_j, edge_attr], dim=-1)

        # Transform to message
        message = self.message_transform(combined)

        # Apply edge gating
        gate = self.edge_gate(edge_attr)

        return message * gate


class ALIGNNConv(nn.Module):
    """
    ALIGNN convolution layer combining line graph and atomic graph convolutions.

    This is the core layer of ALIGNN that processes both the line graph
    (for three-body interactions) and the atomic graph (for two-body interactions).

    Args:
        node_features (int): Number of node features
        edge_features (int): Number of edge features
        hidden_channels (int): Hidden layer dimensions
        dropout (float): Dropout rate
    """

    def __init__(self, node_features: int, edge_features: int,
                 hidden_channels: int, dropout: float = 0.0):
        super().__init__()

        self.node_features = node_features
        self.edge_features = edge_features
        self.hidden_channels = hidden_channels
        self.dropout = dropout

        # Line graph convolution (for three-body interactions)
        self.line_graph_conv = EdgeGatedGraphConv(
            in_channels=edge_features,
            out_channels=hidden_channels,
            edge_dim=4,  # Line graph edge features (angle info)
            dropout=dropout
        )

        # Atomic graph convolution (for two-body interactions)
        self.atomic_graph_conv = EdgeGatedGraphConv(
            in_channels=node_features,
            out_channels=hidden_channels,
            edge_dim=hidden_channels,  # Updated edge features from line graph
            dropout=dropout
        )

        # Edge feature update
        self.edge_update = nn.Sequential(
            nn.Linear(edge_features + hidden_channels, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # Node feature update
        self.node_update = nn.Sequential(
            nn.Linear(node_features + hidden_channels, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # Layer normalization
        self.node_norm = nn.LayerNorm(hidden_channels)
        self.edge_norm = nn.LayerNorm(hidden_channels)

    def forward(self, atomic_graph_data, line_graph_data):
        """
        Forward pass of ALIGNN convolution.

        Args:
            atomic_graph_data: Atomic graph data (nodes=atoms, edges=bonds)
            line_graph_data: Line graph data (nodes=bonds, edges=angles)

        Returns:
            tuple: Updated (atomic_graph_features, line_graph_features)
        """
        # Extract data
        x_atomic = atomic_graph_data.x
        edge_index_atomic = atomic_graph_data.edge_index
        edge_attr_atomic = atomic_graph_data.edge_attr

        x_line = line_graph_data.x
        edge_index_line = line_graph_data.edge_index
        edge_attr_line = line_graph_data.edge_attr

        # Step 1: Update line graph (three-body interactions)
        if edge_index_line.size(1) > 0:
            x_line_updated = self.line_graph_conv(x_line, edge_index_line, edge_attr_line)
            x_line_updated = self.edge_norm(x_line_updated)
        else:
            # Handle case with no line graph edges
            x_line_updated = torch.zeros(x_line.size(0), self.hidden_channels,
                                       device=x_line.device, dtype=x_line.dtype)

        # Step 2: Update edge features in atomic graph using line graph information
        if x_line.size(0) > 0:
            # Combine original edge features with line graph updates
            edge_attr_combined = torch.cat([edge_attr_atomic, x_line_updated], dim=-1)
            edge_attr_updated = self.edge_update(edge_attr_combined)
        else:
            # Fallback if no line graph nodes
            edge_attr_updated = self.edge_update(
                torch.cat([edge_attr_atomic,
                          torch.zeros(edge_attr_atomic.size(0), self.hidden_channels,
                                    device=edge_attr_atomic.device)], dim=-1)
            )

        edge_attr_updated = self.edge_norm(edge_attr_updated)

        # Step 3: Update atomic graph (two-body interactions)
        x_atomic_updated = self.atomic_graph_conv(
            x_atomic, edge_index_atomic, edge_attr_updated
        )
        x_atomic_updated = self.node_norm(x_atomic_updated)

        # Step 4: Update node features
        x_atomic_combined = torch.cat([x_atomic, x_atomic_updated], dim=-1)
        x_atomic_final = self.node_update(x_atomic_combined)

        # Create updated data objects
        from torch_geometric.data import Data

        updated_atomic_graph = Data(
            x=x_atomic_final,
            edge_index=edge_index_atomic,
            edge_attr=edge_attr_updated,
            pos=atomic_graph_data.pos if hasattr(atomic_graph_data, 'pos') else None,
            num_nodes=atomic_graph_data.num_nodes
        )

        updated_line_graph = Data(
            x=x_line_updated,
            edge_index=edge_index_line,
            edge_attr=edge_attr_line,
            num_nodes=line_graph_data.num_nodes
        )

        return updated_atomic_graph, updated_line_graph
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import global_mean_pool, global_max_pool, global_add_pool
from typing import Dict, Any



class ALIGNN(nn.Module):
    """
    Atomistic Line Graph Neural Network (ALIGNN) for crystal property prediction.

    ALIGNN processes both atomic graphs (representing two-body interactions)
    and line graphs (representing three-body interactions) to predict
    material properties.

    Args:
        node_features (int): Number of input node features
        edge_features (int): Number of input edge features
        hidden_channels (int): Hidden layer dimensions
        num_layers (int): Number of ALIGNN convolution layers
        num_classes (int): Number of output classes/properties
        dropout (float): Dropout rate
        pooling (str): Global pooling method ('mean', 'max', 'add')
        use_batch_norm (bool): Whether to use batch normalization
    """

    def __init__(self, node_features: int, edge_features: int,
                 hidden_channels: int = 128, num_layers: int = 4,
                 num_classes: int = 1, dropout: float = 0.1,
                 pooling: str = 'mean', use_batch_norm: bool = True):
        super().__init__()

        self.node_features = node_features
        self.edge_features = edge_features
        self.hidden_channels = hidden_channels
        self.num_layers = num_layers
        self.num_classes = num_classes
        self.dropout = dropout
        self.pooling = pooling
        self.use_batch_norm = use_batch_norm

        # Input embeddings
        self.node_embedding = nn.Sequential(
            nn.Linear(node_features, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        self.edge_embedding = nn.Sequential(
            nn.Linear(edge_features, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # ALIGNN convolution layers
        self.alignn_layers = nn.ModuleList([
            ALIGNNConv(
                node_features=hidden_channels if i > 0 else hidden_channels,
                edge_features=hidden_channels if i > 0 else hidden_channels,
                hidden_channels=hidden_channels,
                dropout=dropout
            )
            for i in range(num_layers)
        ])

        # Batch normalization layers
        if use_batch_norm:
            self.node_batch_norms = nn.ModuleList([
                nn.BatchNorm1d(hidden_channels) for _ in range(num_layers)
            ])
            self.edge_batch_norms = nn.ModuleList([
                nn.BatchNorm1d(hidden_channels) for _ in range(num_layers)
            ])

        # Global pooling
        if pooling == 'mean':
            self.pool = global_mean_pool
        elif pooling == 'max':
            self.pool = global_max_pool
        elif pooling == 'add':
            self.pool = global_add_pool
        else:
            raise ValueError(f"Unknown pooling method: {pooling}")

        # Output layers
        self.classifier = nn.Sequential(
            nn.Linear(hidden_channels, hidden_channels // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_channels // 2, hidden_channels // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_channels // 4, num_classes)
        )

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.zeros_(module.bias)

    def forward(self, batch_data: Dict[str, Any]) -> torch.Tensor:
        """
        Forward pass of ALIGNN model.

        Args:
            batch_data: Dictionary containing:
                - 'graph': Batched atomic graphs
                - 'line_graph': Batched line graphs
                - 'batch': Batch indices for atomic graphs
                - 'line_batch': Batch indices for line graphs (optional)

        Returns:
            Predicted property values [batch_size, num_classes]
        """
        atomic_graph = batch_data['graph']
        line_graph = batch_data['line_graph']
        batch = batch_data.get('batch', None)

        # Handle batch indices
        if batch is None:
            batch = torch.zeros(atomic_graph.x.size(0), dtype=torch.long,
                              device=atomic_graph.x.device)

        # Initial embeddings
        x_atomic = self.node_embedding(atomic_graph.x)
        edge_attr_atomic = self.edge_embedding(atomic_graph.edge_attr)

        # Handle line graph features
        if line_graph.x.size(0) > 0:
            x_line = line_graph.x
            if x_line.size(1) != self.hidden_channels:
                # If line graph features don't match hidden channels, embed them
                line_embed = nn.Linear(x_line.size(1), self.hidden_channels).to(x_line.device)
                x_line = line_embed(x_line)
        else:
            # Create empty line graph features if no line graph
            x_line = torch.empty(0, self.hidden_channels, device=atomic_graph.x.device)

        # Update atomic graph data
        from torch_geometric.data import Data
        atomic_graph_updated = Data(
            x=x_atomic,
            edge_index=atomic_graph.edge_index,
            edge_attr=edge_attr_atomic,
            pos=atomic_graph.pos if hasattr(atomic_graph, 'pos') else None,
            num_nodes=atomic_graph.num_nodes
        )

        line_graph_updated = Data(
            x=x_line,
            edge_index=line_graph.edge_index,
            edge_attr=line_graph.edge_attr,
            num_nodes=line_graph.num_nodes
        )

        # Apply ALIGNN layers
        for i, alignn_layer in enumerate(self.alignn_layers):
            # Apply ALIGNN convolution
            atomic_graph_updated, line_graph_updated = alignn_layer(
                atomic_graph_updated, line_graph_updated
            )

            # Apply batch normalization if enabled
            if self.use_batch_norm:
                if atomic_graph_updated.x.size(0) > 0:
                    atomic_graph_updated.x = self.node_batch_norms[i](atomic_graph_updated.x)
                if line_graph_updated.x.size(0) > 0:
                    line_graph_updated.x = self.edge_batch_norms[i](line_graph_updated.x)

            # Apply activation and dropout
            atomic_graph_updated.x = F.relu(atomic_graph_updated.x)
            atomic_graph_updated.x = F.dropout(atomic_graph_updated.x,
                                             p=self.dropout, training=self.training)

        # Global pooling
        graph_representation = self.pool(atomic_graph_updated.x, batch)

        # Final prediction
        output = self.classifier(graph_representation)

        return output

    def get_embeddings(self, batch_data: Dict[str, Any]) -> torch.Tensor:
        """
        Get graph embeddings without final classification layer.

        Args:
            batch_data: Dictionary containing graph data

        Returns:
            Graph embeddings [batch_size, hidden_channels]
        """
        atomic_graph = batch_data['graph']
        line_graph = batch_data['line_graph']
        batch = batch_data.get('batch', None)

        if batch is None:
            batch = torch.zeros(atomic_graph.x.size(0), dtype=torch.long,
                              device=atomic_graph.x.device)

        # Forward pass without final classifier
        x_atomic = self.node_embedding(atomic_graph.x)
        edge_attr_atomic = self.edge_embedding(atomic_graph.edge_attr)

        # Handle line graph features
        if line_graph.x.size(0) > 0:
            x_line = line_graph.x
            if x_line.size(1) != self.hidden_channels:
                line_embed = nn.Linear(x_line.size(1), self.hidden_channels).to(x_line.device)
                x_line = line_embed(x_line)
        else:
            x_line = torch.empty(0, self.hidden_channels, device=atomic_graph.x.device)

        from torch_geometric.data import Data
        atomic_graph_updated = Data(
            x=x_atomic,
            edge_index=atomic_graph.edge_index,
            edge_attr=edge_attr_atomic,
            pos=atomic_graph.pos if hasattr(atomic_graph, 'pos') else None,
            num_nodes=atomic_graph.num_nodes
        )

        line_graph_updated = Data(
            x=x_line,
            edge_index=line_graph.edge_index,
            edge_attr=line_graph.edge_attr,
            num_nodes=line_graph.num_nodes
        )

        # Apply ALIGNN layers
        for i, alignn_layer in enumerate(self.alignn_layers):
            atomic_graph_updated, line_graph_updated = alignn_layer(
                atomic_graph_updated, line_graph_updated
            )

            if self.use_batch_norm:
                if atomic_graph_updated.x.size(0) > 0:
                    atomic_graph_updated.x = self.node_batch_norms[i](atomic_graph_updated.x)

            atomic_graph_updated.x = F.relu(atomic_graph_updated.x)
            atomic_graph_updated.x = F.dropout(atomic_graph_updated.x,
                                             p=self.dropout, training=self.training)

        # Global pooling to get graph embeddings
        embeddings = self.pool(atomic_graph_updated.x, batch)

        return embeddings
import torch
from torch_geometric.data import Batch
from typing import List, Dict, Any


def collate_alignn_batch(batch: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Collate function for ALIGNN data loader.

    This function batches multiple graph samples into a single batch
    suitable for ALIGNN model training.

    Args:
        batch: List of sample dictionaries from ALIGNNDataset

    Returns:
        Batched data dictionary
    """
    # Separate atomic graphs and line graphs
    atomic_graphs = [sample['graph'] for sample in batch]
    line_graphs = [sample['line_graph'] for sample in batch]
    targets = [sample['target'] for sample in batch]
    material_ids = [sample['material_id'] for sample in batch]

    # Batch atomic graphs
    batched_atomic = Batch.from_data_list(atomic_graphs)

    # Batch line graphs (more complex due to variable sizes)
    batched_line = batch_line_graphs(line_graphs)

    # Stack targets
    batched_targets = torch.stack(targets, dim=0)

    return {
        'graph': batched_atomic,
        'line_graph': batched_line,
        'target': batched_targets,
        'material_ids': material_ids,
        'batch': batched_atomic.batch
    }


def batch_line_graphs(line_graphs: List) -> Batch:
    """
    Batch line graphs with special handling for empty graphs.

    Args:
        line_graphs: List of line graph Data objects

    Returns:
        Batched line graph
    """
    # Filter out empty line graphs and keep track of indices
    non_empty_graphs = []
    empty_indices = []

    for i, graph in enumerate(line_graphs):
        if graph.num_nodes > 0:
            non_empty_graphs.append(graph)
        else:
            empty_indices.append(i)

    if non_empty_graphs:
        # Batch non-empty graphs
        batched = Batch.from_data_list(non_empty_graphs)

        # If there were empty graphs, we need to handle them
        if empty_indices:
            # Create a mapping from original indices to batched indices
            batch_mapping = torch.zeros(len(line_graphs), dtype=torch.long)
            non_empty_idx = 0
            for i in range(len(line_graphs)):
                if i not in empty_indices:
                    batch_mapping[i] = non_empty_idx
                    non_empty_idx += 1
                else:
                    batch_mapping[i] = -1  # Mark empty graphs

        return batched
    else:
        # All graphs are empty, create an empty batch
        from torch_geometric.data import Data
        empty_graph = Data(
            x=torch.empty(0, 4),  # Assuming 4 edge features
            edge_index=torch.empty(2, 0, dtype=torch.long),
            edge_attr=torch.empty(0, 4),
            num_nodes=0
        )
        return Batch.from_data_list([empty_graph])


def prepare_alignn_input(sample: Dict[str, Any], device: torch.device) -> Dict[str, Any]:
    """
    Prepare a single sample for ALIGNN model input.

    Args:
        sample: Sample dictionary from dataset
        device: Target device

    Returns:
        Prepared input dictionary
    """
    # Move data to device
    graph = sample['graph'].to(device)
    line_graph = sample['line_graph'].to(device)
    target = sample['target'].to(device)

    # Create batch indices for single sample
    batch = torch.zeros(graph.num_nodes, dtype=torch.long, device=device)

    return {
        'graph': graph,
        'line_graph': line_graph,
        'target': target,
        'batch': batch,
        'material_id': sample['material_id']
    }


def compute_graph_statistics(dataset) -> Dict[str, float]:
    """
    Compute statistics about the graphs in the dataset.

    Args:
        dataset: ALIGNN dataset

    Returns:
        Dictionary of statistics
    """
    num_atoms_list = []
    num_bonds_list = []
    num_line_nodes_list = []
    num_line_edges_list = []

    for i in range(min(1000, len(dataset))):  # Sample first 1000 for efficiency
        sample = dataset[i]

        num_atoms_list.append(sample['graph'].num_nodes)
        num_bonds_list.append(sample['graph'].edge_index.size(1))
        num_line_nodes_list.append(sample['line_graph'].num_nodes)
        num_line_edges_list.append(sample['line_graph'].edge_index.size(1))

    stats = {
        'avg_num_atoms': float(torch.tensor(num_atoms_list, dtype=torch.float).mean()),
        'max_num_atoms': max(num_atoms_list),
        'min_num_atoms': min(num_atoms_list),
        'avg_num_bonds': float(torch.tensor(num_bonds_list, dtype=torch.float).mean()),
        'max_num_bonds': max(num_bonds_list),
        'min_num_bonds': min(num_bonds_list),
        'avg_num_line_nodes': float(torch.tensor(num_line_nodes_list, dtype=torch.float).mean()),
        'max_num_line_nodes': max(num_line_nodes_list),
        'min_num_line_nodes': min(num_line_nodes_list),
        'avg_num_line_edges': float(torch.tensor(num_line_edges_list, dtype=torch.float).mean()),
        'max_num_line_edges': max(num_line_edges_list),
        'min_num_line_edges': min(num_line_edges_list),
    }

    return stats


def normalize_targets(targets: torch.Tensor, mean: float = None, std: float = None):
    """
    Normalize target values.

    Args:
        targets: Target tensor
        mean: Mean for normalization (computed if None)
        std: Standard deviation for normalization (computed if None)

    Returns:
        tuple: (normalized_targets, mean, std)
    """
    if mean is None:
        mean = targets.mean().item()
    if std is None:
        std = targets.std().item()

    normalized = (targets - mean) / (std + 1e-8)

    return normalized, mean, std


def denormalize_targets(normalized_targets: torch.Tensor, mean: float, std: float):
    """
    Denormalize target values.

    Args:
        normalized_targets: Normalized target tensor
        mean: Mean used for normalization
        std: Standard deviation used for normalization

    Returns:
        Denormalized targets
    """
    return normalized_targets * std + mean

import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, List, Tuple, Optional
from tqdm import tqdm




def evaluate_alignn(model: nn.Module, data_loader: DataLoader, device: torch.device,
                   target_mean: float = 0.0, target_std: float = 1.0,
                   return_predictions: bool = False) -> Dict[str, Any]:
    """
    Evaluate ALIGNN model on a dataset.

    Args:
        model: Trained ALIGNN model
        data_loader: Data loader for evaluation
        device: Device to run evaluation on
        target_mean: Mean of targets for denormalization
        target_std: Standard deviation of targets for denormalization
        return_predictions: Whether to return predictions and targets

    Returns:
        Dictionary containing evaluation metrics
    """
    model.eval()
    model = model.to(device)

    all_predictions = []
    all_targets = []
    all_material_ids = []
    total_loss = 0.0
    num_batches = 0

    criterion = nn.MSELoss()

    with torch.no_grad():
        pbar = tqdm(data_loader, desc="Evaluating")
        for batch in pbar:
            batch = move_batch_to_device(batch, device)

            # Forward pass
            predictions = model(batch)
            targets = batch['target']

            # Compute loss
            loss = criterion(predictions, targets)
            total_loss += loss.item()
            num_batches += 1

            # Denormalize predictions and targets
            pred_denorm = denormalize_targets(predictions, target_mean, target_std)
            target_denorm = denormalize_targets(targets, target_mean, target_std)

            # Store results
            all_predictions.extend(pred_denorm.cpu().numpy().flatten())
            all_targets.extend(target_denorm.cpu().numpy().flatten())
            all_material_ids.extend(batch['material_ids'])

            # Update progress bar
            mae = np.mean(np.abs(np.array(all_predictions) - np.array(all_targets)))
            pbar.set_postfix({'MAE': f"{mae:.4f}"})

    # Convert to numpy arrays
    predictions = np.array(all_predictions)
    targets = np.array(all_targets)

    # Compute metrics
    mae = mean_absolute_error(targets, predictions)
    mse = mean_squared_error(targets, predictions)
    rmse = np.sqrt(mse)
    r2 = r2_score(targets, predictions)

    # Additional metrics
    mape = np.mean(np.abs((targets - predictions) / (targets + 1e-8))) * 100
    max_error = np.max(np.abs(targets - predictions))

    # Compute percentile errors
    errors = np.abs(targets - predictions)
    percentile_50 = np.percentile(errors, 50)
    percentile_90 = np.percentile(errors, 90)
    percentile_95 = np.percentile(errors, 95)

    metrics = {
        'mae': mae,
        'mse': mse,
        'rmse': rmse,
        'r2': r2,
        'mape': mape,
        'max_error': max_error,
        'median_error': percentile_50,
        'p90_error': percentile_90,
        'p95_error': percentile_95,
        'avg_loss': total_loss / num_batches,
        'num_samples': len(predictions)
    }

    if return_predictions:
        metrics['predictions'] = predictions
        metrics['targets'] = targets
        metrics['material_ids'] = all_material_ids

    return metrics


def plot_predictions(targets: np.ndarray, predictions: np.ndarray,
                    title: str = "ALIGNN Predictions vs Targets",
                    save_path: str = None, show_plot: bool = True) -> plt.Figure:
    """
    Plot predictions vs targets with error analysis.

    Args:
        targets: True target values
        predictions: Predicted values
        title: Plot title
        save_path: Path to save plot
        show_plot: Whether to display plot

    Returns:
        Matplotlib figure
    """
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle(title, fontsize=16)

    # Scatter plot
    ax1 = axes[0, 0]
    ax1.scatter(targets, predictions, alpha=0.6, s=20)

    # Perfect prediction line
    min_val = min(targets.min(), predictions.min())
    max_val = max(targets.max(), predictions.max())
    ax1.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Perfect Prediction')

    ax1.set_xlabel('True Values')
    ax1.set_ylabel('Predicted Values')
    ax1.set_title('Predictions vs Targets')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Add R² to the plot
    r2 = r2_score(targets, predictions)
    ax1.text(0.05, 0.95, f'R² = {r2:.3f}', transform=ax1.transAxes,
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    # Residual plot
    ax2 = axes[0, 1]
    residuals = predictions - targets
    ax2.scatter(targets, residuals, alpha=0.6, s=20)
    ax2.axhline(y=0, color='r', linestyle='--', lw=2)
    ax2.set_xlabel('True Values')
    ax2.set_ylabel('Residuals (Predicted - True)')
    ax2.set_title('Residual Plot')
    ax2.grid(True, alpha=0.3)

    # Error distribution
    ax3 = axes[1, 0]
    errors = np.abs(residuals)
    ax3.hist(errors, bins=50, alpha=0.7, edgecolor='black')
    ax3.set_xlabel('Absolute Error')
    ax3.set_ylabel('Frequency')
    ax3.set_title('Error Distribution')
    ax3.grid(True, alpha=0.3)

    # Add statistics
    mae = np.mean(errors)
    rmse = np.sqrt(np.mean(residuals**2))
    ax3.axvline(mae, color='red', linestyle='--', label=f'MAE = {mae:.3f}')
    ax3.legend()

    # Q-Q plot for residuals
    ax4 = axes[1, 1]
    from scipy import stats
    stats.probplot(residuals, dist="norm", plot=ax4)
    ax4.set_title('Q-Q Plot of Residuals')
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    if show_plot:
        plt.show()

    return fig


def analyze_model_performance(metrics: Dict[str, Any], dataset_name: str = "Dataset") -> str:
    """
    Generate a performance analysis report.

    Args:
        metrics: Dictionary of evaluation metrics
        dataset_name: Name of the dataset

    Returns:
        Performance analysis report as string
    """
    report = f"""
ALIGNN Model Performance Analysis - {dataset_name}
{'='*60}

Overall Metrics:
- Mean Absolute Error (MAE): {metrics['mae']:.4f}
- Root Mean Square Error (RMSE): {metrics['rmse']:.4f}
- R² Score: {metrics['r2']:.4f}
- Mean Absolute Percentage Error (MAPE): {metrics['mape']:.2f}%

Error Distribution:
- Median Error: {metrics['median_error']:.4f}
- 90th Percentile Error: {metrics['p90_error']:.4f}
- 95th Percentile Error: {metrics['p95_error']:.4f}
- Maximum Error: {metrics['max_error']:.4f}

Dataset Information:
- Number of Samples: {metrics['num_samples']:,}
- Average Loss: {metrics['avg_loss']:.6f}

Performance Assessment:
"""

    # Performance assessment based on R²
    if metrics['r2'] >= 0.9:
        assessment = "Excellent"
    elif metrics['r2'] >= 0.8:
        assessment = "Good"
    elif metrics['r2'] >= 0.7:
        assessment = "Fair"
    else:
        assessment = "Poor"

    report += f"- Overall Performance: {assessment} (R² = {metrics['r2']:.3f})\n"

    # Error analysis
    if metrics['mape'] < 5:
        error_assessment = "Very Low"
    elif metrics['mape'] < 10:
        error_assessment = "Low"
    elif metrics['mape'] < 20:
        error_assessment = "Moderate"
    else:
        error_assessment = "High"

    report += f"- Error Level: {error_assessment} (MAPE = {metrics['mape']:.1f}%)\n"

    return report


def compare_models(results_dict: Dict[str, Dict[str, Any]],
                  save_path: str = None) -> plt.Figure:
    """
    Compare performance of multiple models.

    Args:
        results_dict: Dictionary with model names as keys and metrics as values
        save_path: Path to save comparison plot

    Returns:
        Matplotlib figure
    """
    models = list(results_dict.keys())
    metrics_to_compare = ['mae', 'rmse', 'r2', 'mape']

    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle('Model Performance Comparison', fontsize=16)

    for i, metric in enumerate(metrics_to_compare):
        ax = axes[i // 2, i % 2]

        values = [results_dict[model][metric] for model in models]

        bars = ax.bar(models, values, alpha=0.7)
        ax.set_title(f'{metric.upper()}')
        ax.set_ylabel(metric.upper())

        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:.3f}', ha='center', va='bottom')

        # Rotate x-axis labels if needed
        if len(max(models, key=len)) > 8:
            ax.tick_params(axis='x', rotation=45)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    return fig

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import logging
from typing import Dict, Any, Tuple, Optional


def train_alignn(model: nn.Module, train_loader: DataLoader, val_loader: DataLoader,
                 num_epochs: int, learning_rate: float = 1e-3, weight_decay: float = 1e-5,
                 device: torch.device = None, save_path: str = None,
                 target_mean: float = 0.0, target_std: float = 1.0,
                 patience: int = 20, min_delta: float = 1e-4) -> Dict[str, Any]:
    """
    Train ALIGNN model.

    Args:
        model: ALIGNN model to train
        train_loader: Training data loader
        val_loader: Validation data loader
        num_epochs: Number of training epochs
        learning_rate: Learning rate for optimizer
        weight_decay: Weight decay for regularization
        device: Device to train on
        save_path: Path to save best model
        target_mean: Mean of targets for denormalization
        target_std: Standard deviation of targets for denormalization
        patience: Early stopping patience
        min_delta: Minimum improvement for early stopping

    Returns:
        Training history dictionary
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    model = model.to(device)

    # Setup optimizer and scheduler
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=10, verbose=True
    )

    # Loss function
    criterion = nn.MSELoss()

    # Training history
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_mae': [],
        'val_mae': [],
        'learning_rates': []
    }

    best_val_loss = float('inf')
    epochs_without_improvement = 0

    logging.info(f"Starting training on {device}")
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_losses = []
        train_maes = []

        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Train]")
        for batch in train_pbar:
            optimizer.zero_grad()

            # Move batch to device
            batch = move_batch_to_device(batch, device)

            # Forward pass
            predictions = model(batch)
            targets = batch['target']

            # Compute loss
            loss = criterion(predictions, targets)

            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            # Compute metrics
            with torch.no_grad():
                # Denormalize for MAE calculation
                pred_denorm = denormalize_targets(predictions, target_mean, target_std)
                target_denorm = denormalize_targets(targets, target_mean, target_std)
                mae = torch.mean(torch.abs(pred_denorm - target_denorm))

                train_losses.append(loss.item())
                train_maes.append(mae.item())

            train_pbar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'mae': f"{mae.item():.4f}"
            })

        # Validation phase
        model.eval()
        val_losses = []
        val_maes = []

        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Val]")
            for batch in val_pbar:
                batch = move_batch_to_device(batch, device)

                predictions = model(batch)
                targets = batch['target']

                loss = criterion(predictions, targets)

                # Denormalize for MAE calculation
                pred_denorm = denormalize_targets(predictions, target_mean, target_std)
                target_denorm = denormalize_targets(targets, target_mean, target_std)
                mae = torch.mean(torch.abs(pred_denorm - target_denorm))

                val_losses.append(loss.item())
                val_maes.append(mae.item())

                val_pbar.set_postfix({
                    'loss': f"{loss.item():.4f}",
                    'mae': f"{mae.item():.4f}"
                })

        # Calculate epoch metrics
        epoch_train_loss = np.mean(train_losses)
        epoch_val_loss = np.mean(val_losses)
        epoch_train_mae = np.mean(train_maes)
        epoch_val_mae = np.mean(val_maes)
        current_lr = optimizer.param_groups[0]['lr']

        # Update history
        history['train_loss'].append(epoch_train_loss)
        history['val_loss'].append(epoch_val_loss)
        history['train_mae'].append(epoch_train_mae)
        history['val_mae'].append(epoch_val_mae)
        history['learning_rates'].append(current_lr)

        # Learning rate scheduling
        scheduler.step(epoch_val_loss)

        # Early stopping check
        if epoch_val_loss < best_val_loss - min_delta:
            best_val_loss = epoch_val_loss
            epochs_without_improvement = 0

            # Save best model
            if save_path:
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'epoch': epoch,
                    'val_loss': epoch_val_loss,
                    'target_mean': target_mean,
                    'target_std': target_std,
                    'history': history
                }, save_path)
        else:
            epochs_without_improvement += 1

        # Log epoch results
        logging.info(
            f"Epoch {epoch+1}/{num_epochs}: "
            f"Train Loss: {epoch_train_loss:.4f}, Val Loss: {epoch_val_loss:.4f}, "
            f"Train MAE: {epoch_train_mae:.4f}, Val MAE: {epoch_val_mae:.4f}, "
            f"LR: {current_lr:.2e}"
        )

        # Early stopping
        if epochs_without_improvement >= patience:
            logging.info(f"Early stopping after {epoch+1} epochs")
            break

    return history


def move_batch_to_device(batch: Dict[str, Any], device: torch.device) -> Dict[str, Any]:
    """Move batch data to specified device."""
    moved_batch = {}
    for key, value in batch.items():
        if key == 'material_ids':
            moved_batch[key] = value  # Keep strings on CPU
        elif hasattr(value, 'to'):
            moved_batch[key] = value.to(device)
        else:
            moved_batch[key] = value
    return moved_batch


def setup_logging(log_file: str = None, level: int = logging.INFO):
    """Setup logging configuration."""
    handlers = [logging.StreamHandler()]
    if log_file:
        handlers.append(logging.FileHandler(log_file))

    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=handlers
    )


def save_training_checkpoint(model: nn.Module, optimizer: optim.Optimizer,
                           epoch: int, loss: float, path: str,
                           additional_info: Dict[str, Any] = None):
    """Save training checkpoint."""
    checkpoint = {
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'epoch': epoch,
        'loss': loss
    }

    if additional_info:
        checkpoint.update(additional_info)

    torch.save(checkpoint, path)


def load_training_checkpoint(model: nn.Module, optimizer: optim.Optimizer,
                           path: str, device: torch.device) -> Dict[str, Any]:
    """Load training checkpoint."""
    checkpoint = torch.load(path, map_location=device)

    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

    return checkpoint
