import os
import json
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, random_split
from torch_geometric.data import Data, Batch
from torch_geometric.nn import MessagePassing, global_mean_pool
from torch_geometric.utils import add_self_loops

import matplotlib.pyplot as plt
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from tqdm import tqdm
import logging
from typing import Dict, Any, List, Tuple, Optional
# import argparse # Removed argparse
import sys
from pathlib import Path



class ALIGNNDataset(Dataset):
    """ALIGNN Dataset compatible with CGN-E3 data format."""

    def __init__(self, path, target_name, cutoff=8.0, add_self_loops=True):
        super().__init__()
        self.path = path
        self.target_name = target_name
        self.cutoff = cutoff
        self.add_self_loops = add_self_loops

        # Load all required data
        self._load_graph_data()
        self._load_config()
        self._load_targets()

        # Validate data consistency
        if len(self.graph_data) != len(self.targets):
            raise ValueError(
                f"Graph count ({len(self.graph_data)}) doesn't match "
                f"target count ({len(self.targets)})"
            )

    def _load_graph_data(self):
        """Load and validate graph data from NPZ file."""
        npz_path = os.path.join(self.path, "BandgapTargets.npz")
        try:
            with np.load(npz_path, allow_pickle=True) as data:
                graph_dict = data['graph_dict'].item()
                self.graph_names = list(graph_dict.keys())
                self.graph_data = []

                for name, graph in graph_dict.items():
                    try:
                        # Assuming Graph class is defined elsewhere and handles dictionary input
                        # Add checks for required keys in the graph dictionary
                        if 'cart_coords' not in graph:
                             raise ValueError(f"Missing cart_coords in graph {name}")
                        # Placeholder for actual Graph class instantiation
                        # Replace with your actual Graph class logic
                        # self.graph_data.append(Graph(graph))
                        # For now, just append the raw dictionary if a Graph class is not available
                        self.graph_data.append(graph)
                    except ValueError as e:
                        print(f"Skipping invalid graph {name}: {str(e)}")
                        continue

                if not self.graph_data:
                    raise ValueError("No valid graphs found in NPZ file")

                # Debug: Print structure of first graph to help identify keys
                if self.graph_data:
                    first_graph = self.graph_data[0]
                    print(f"Loaded {len(self.graph_data)} graphs")
                    print(f"Sample graph keys: {list(first_graph.keys())}")

                    # Check what node information is available
                    node_keys = [k for k in first_graph.keys() if 'node' in k.lower() or k in ['nodes', 'atomic_numbers']]
                    print(f"Potential node keys: {node_keys}")

        except Exception as e:
            raise RuntimeError(f"Error loading graph data: {str(e)}")


    def _load_config(self):
        """Load and validate configuration."""
        config_path = os.path.join(self.path, "BandgapTargets_config.json")
        try:
            with open(config_path) as f:
                config = json.load(f)

            self.atomic_numbers = config["atomic_numbers"]
            self.node_vectors = np.array(config["node_vectors"])
            self.n_node_feat = len(self.node_vectors[0])
            self.pos_dim = config.get("pos_dim", 3)

            # Create atomic number to index mapping
            self.atomic_to_idx = {
                num: idx for idx, num in enumerate(self.atomic_numbers)
            }

            if len(self.atomic_to_idx) != len(self.atomic_numbers):
                raise ValueError("Duplicate atomic numbers in config")

        except Exception as e:
            raise RuntimeError(f"Error loading config: {str(e)}")

    def _load_targets(self):
        """Load target values from CSV file."""
        targets_path = os.path.join(self.path, "BandgapTargets.csv")
        try:
            df = pd.read_csv(targets_path)

            if self.target_name not in df.columns:
                raise ValueError(
                    f"Target column '{self.target_name}' not found in CSV"
                )

            self.targets = df[self.target_name].values
            if len(self.targets) == 0:
                raise ValueError("No targets found in CSV file")

            # Update graph names from CSV
            self.graph_names = df['mpid'].values.tolist()

        except Exception as e:
            raise RuntimeError(f"Error loading targets: {str(e)}")

    def __len__(self):
        """Return the number of samples in the dataset."""
        return len(self.graph_data)

    def __getitem__(self, index):
        """Get a single sample."""
        graph = self.graph_data[index]

        # Debug: Print graph structure for first few samples
        if index < 3:
            print(f"Graph {index} keys: {list(graph.keys())}")

        # Handle different possible key names for node features
        if 'nodes' in graph:
            node_list = graph['nodes']
            print(f"Using 'nodes' key, found {len(node_list)} nodes")
        elif 'node_features' in graph:
            node_list = graph['node_features']
            print(f"Using 'node_features' key, found {len(node_list)} nodes")
        elif 'atomic_numbers' in graph:
            node_list = graph['atomic_numbers']
            print(f"Using 'atomic_numbers' key, found {len(node_list)} nodes")
        else:
            print(f"ERROR: Could not find node information in graph {index}")
            print(f"Available keys: {list(graph.keys())}")
            # Try to find any key that might contain node information
            potential_keys = [k for k in graph.keys() if any(term in k.lower() for term in ['node', 'atom', 'element'])]
            print(f"Potential node-related keys: {potential_keys}")
            raise KeyError(f"Could not find node information in graph. Available keys: {list(graph.keys())}")

        # Create node features from atomic numbers
        node_features = np.zeros((len(node_list), self.n_node_feat))
        for i, atomic_num in enumerate(node_list):
            if atomic_num in self.atomic_to_idx:
                idx = self.atomic_to_idx[atomic_num]
                node_features[i] = self.node_vectors[idx]
            else:
                print(f"Warning: Atomic number {atomic_num} not found in config, using zeros")

        # Handle position data
        if isinstance(graph['cart_coords'], torch.Tensor):
            pos = graph['cart_coords'].clone().detach()
        else:
            pos = torch.tensor(graph['cart_coords'], dtype=torch.float32)

        # Handle edge data - create edges if not present
        if 'edge_index' in graph and 'edge_attr' in graph:
            edge_index = torch.tensor(graph['edge_index'], dtype=torch.long)
            edge_attr = torch.tensor(graph['edge_attr'], dtype=torch.float32)
        else:
            # Create simple distance-based edges if not provided
            num_nodes = len(node_list)
            edge_index = []
            edge_attr = []

            for i in range(num_nodes):
                for j in range(i+1, num_nodes):
                    dist = torch.norm(pos[i] - pos[j])
                    if dist < 8.0:  # cutoff distance
                        edge_index.extend([[i, j], [j, i]])
                        edge_attr.extend([dist.item(), dist.item()])

            if edge_index:
                edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
                edge_attr = torch.tensor(edge_attr, dtype=torch.float32).unsqueeze(1)
            else:
                # No edges found, create minimal structure
                edge_index = torch.empty((2, 0), dtype=torch.long)
                edge_attr = torch.empty((0, 1), dtype=torch.float32)

        # Create original atomic graph
        atomic_graph = Data(
            x=torch.tensor(node_features, dtype=torch.float32),
            edge_index=edge_index,
            edge_attr=edge_attr,
            pos=pos,
            num_nodes=len(node_list)
        )

        # Create line graph for ALIGNN
        _, line_graph = create_line_graph(atomic_graph, cutoff=self.cutoff)

        # Add self-loops to line graph if requested
        if self.add_self_loops:
            line_graph = add_self_loops_to_line_graph(line_graph)

        # Prepare target
        target = torch.tensor([self.targets[index]], dtype=torch.float32)

        return {
            'graph': atomic_graph,
            'line_graph': line_graph,
            'target': target,
            'material_id': self.graph_names[index]
        }

    def get_statistics(self):
        """Get dataset statistics for normalization."""
        targets = np.array(self.targets)
        return {
            'target_mean': float(np.mean(targets)),
            'target_std': float(np.std(targets)),
            'target_min': float(np.min(targets)),
            'target_max': float(np.max(targets)),
            'num_samples': len(targets),
            'num_node_features': self.n_node_feat,
            'atomic_numbers': self.atomic_numbers
        }


# ============================================================================
# LINE GRAPH CONSTRUCTION
# ============================================================================

def create_line_graph(data: Data, cutoff: float = 8.0) -> Tuple[Data, Data]:
    """Create line graph from atomic graph for ALIGNN model."""
    # Extract basic information
    pos = data.pos
    edge_index = data.edge_index
    num_nodes = data.x.size(0)
    num_edges = edge_index.size(1)

    # Create line graph nodes (one for each edge in original graph)
    if hasattr(data, 'edge_attr') and data.edge_attr is not None:
        line_node_features = data.edge_attr
    else:
        # If no edge attributes, create basic features from distances
        edge_vectors = pos[edge_index[1]] - pos[edge_index[0]]
        edge_distances = torch.norm(edge_vectors, dim=1, keepdim=True)
        line_node_features = edge_distances

    # Create line graph edges (connect edges that share a vertex)
    line_edge_sources = []
    line_edge_targets = []
    line_edge_features = []

    # For each node in the original graph, find all edges connected to it
    for node_idx in range(num_nodes):
        # Find all edges connected to this node
        connected_edges = []
        for edge_idx in range(num_edges):
            if edge_index[0, edge_idx] == node_idx or edge_index[1, edge_idx] == node_idx:
                connected_edges.append(edge_idx)

        # Connect all pairs of edges that share this node
        for i, edge_i in enumerate(connected_edges):
            for j, edge_j in enumerate(connected_edges):
                if i != j:  # Don't connect edge to itself
                    line_edge_sources.append(edge_i)
                    line_edge_targets.append(edge_j)

                    # Create edge feature for line graph edge (angle information)
                    edge_i_atoms = [edge_index[0, edge_i].item(), edge_index[1, edge_i].item()]
                    edge_j_atoms = [edge_index[0, edge_j].item(), edge_index[1, edge_j].item()]

                    # Find the shared atom (center of angle)
                    shared_atom = None
                    for atom in edge_i_atoms:
                        if atom in edge_j_atoms:
                            shared_atom = atom
                            break

                    if shared_atom is not None:
                        # Get the other two atoms
                        atom_i = edge_i_atoms[0] if edge_i_atoms[1] == shared_atom else edge_i_atoms[1]
                        atom_j = edge_j_atoms[0] if edge_j_atoms[1] == shared_atom else edge_j_atoms[1]

                        # Calculate angle features
                        vec_i = pos[atom_i] - pos[shared_atom]
                        vec_j = pos[atom_j] - pos[shared_atom]

                        # Cosine of angle
                        cos_angle = torch.dot(vec_i, vec_j) / (torch.norm(vec_i) * torch.norm(vec_j) + 1e-8)

                        # Distance between the two outer atoms
                        distance_ij = torch.norm(pos[atom_i] - pos[atom_j])

                        # Bond lengths
                        bond_i_length = torch.norm(vec_i)
                        bond_j_length = torch.norm(vec_j)

                        line_edge_features.append([
                            cos_angle.item(),
                            distance_ij.item(),
                            bond_i_length.item(),
                            bond_j_length.item()
                        ])
                    else:
                        # Fallback if no shared atom found
                        line_edge_features.append([0.0, 0.0, 0.0, 0.0])

    # Convert to tensors
    if line_edge_sources:
        line_edge_index = torch.tensor([line_edge_sources, line_edge_targets], dtype=torch.long)
        line_edge_attr = torch.tensor(line_edge_features, dtype=torch.float32)
    else:
        # Handle case with no line graph edges
        line_edge_index = torch.empty((2, 0), dtype=torch.long)
        line_edge_attr = torch.empty((0, 4), dtype=torch.float32)

    # Create line graph data object
    line_graph = Data(
        x=line_node_features,
        edge_index=line_edge_index,
        edge_attr=line_edge_attr,
        num_nodes=num_edges
    )

    # Return both original graph and line graph
    return data, line_graph


def add_self_loops_to_line_graph(line_graph: Data) -> Data:
    """Add self-loops to line graph for better message passing."""
    num_nodes = line_graph.x.size(0)

    if num_nodes == 0:
        return line_graph

    # Create self-loop edges
    self_loop_index = torch.arange(num_nodes, dtype=torch.long).repeat(2, 1)

    # Create self-loop features (identity features)
    self_loop_attr = torch.zeros(num_nodes, line_graph.edge_attr.size(1))
    self_loop_attr[:, 0] = 1.0  # Set first feature to 1 for self-loops

    # Concatenate with existing edges
    if line_graph.edge_index.size(1) > 0:
        edge_index = torch.cat([line_graph.edge_index, self_loop_index], dim=1)
        edge_attr = torch.cat([line_graph.edge_attr, self_loop_attr], dim=0)
    else:
        edge_index = self_loop_index
        edge_attr = self_loop_attr

    return Data(
        x=line_graph.x,
        edge_index=edge_index,
        edge_attr=edge_attr,
        num_nodes=num_nodes
    )


# ============================================================================
# ALIGNN MODEL ARCHITECTURE
# ============================================================================

class EdgeGatedGraphConv(MessagePassing):
    """Edge-gated graph convolution layer."""

    def __init__(self, in_channels: int, out_channels: int, edge_dim: int,
                 dropout: float = 0.0, bias: bool = True):
        super().__init__(aggr='add', node_dim=0)

        self.in_channels = in_channels
        self.out_channels = out_channels
        self.edge_dim = edge_dim
        self.dropout = dropout

        # Node transformation
        self.node_transform = nn.Linear(in_channels, out_channels, bias=bias)

        # Edge gating mechanism
        self.edge_gate = nn.Sequential(
            nn.Linear(edge_dim, out_channels),
            nn.Sigmoid()
        )

        # Message transformation
        self.message_transform = nn.Sequential(
            nn.Linear(in_channels + edge_dim, out_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # Self-loop transformation
        self.self_transform = nn.Linear(in_channels, out_channels, bias=bias)

        self.reset_parameters()

    def reset_parameters(self):
        """Reset layer parameters."""
        self.node_transform.reset_parameters()
        for layer in self.edge_gate:
            if hasattr(layer, 'reset_parameters'):
                layer.reset_parameters()
        for layer in self.message_transform:
            if hasattr(layer, 'reset_parameters'):
                layer.reset_parameters()
        self.self_transform.reset_parameters()

    def forward(self, x: torch.Tensor, edge_index, edge_attr=None) -> torch.Tensor:
        """Forward pass of edge-gated graph convolution."""
        if edge_attr is None:
            raise ValueError("Edge attributes are required for EdgeGatedGraphConv")

        # Add self-loops
        edge_index, edge_attr = add_self_loops(
            edge_index, edge_attr, fill_value=0.0, num_nodes=x.size(0)
        )

        # Propagate messages
        out = self.propagate(edge_index, x=x, edge_attr=edge_attr)

        # Add self-connection
        out = out + self.self_transform(x)

        return out

    def message(self, x_j: torch.Tensor, edge_attr: torch.Tensor) -> torch.Tensor:
        """Create messages from source nodes to target nodes."""
        # Combine node and edge features
        combined = torch.cat([x_j, edge_attr], dim=-1)

        # Transform to message
        message = self.message_transform(combined)

        # Apply edge gating
        gate = self.edge_gate(edge_attr)

        return message * gate


class ALIGNNConv(nn.Module):
    """ALIGNN convolution layer combining line graph and atomic graph convolutions."""

    def __init__(self, node_features: int, edge_features: int,
                 hidden_channels: int, dropout: float = 0.0):
        super().__init__()

        self.node_features = node_features
        self.edge_features = edge_features
        self.hidden_channels = hidden_channels
        self.dropout = dropout

        # Line graph convolution (for three-body interactions)
        self.line_graph_conv = EdgeGatedGraphConv(
            in_channels=edge_features,
            out_channels=hidden_channels,
            edge_dim=4,  # Line graph edge features (angle info)
            dropout=dropout
        )

        # Atomic graph convolution (for two-body interactions)
        self.atomic_graph_conv = EdgeGatedGraphConv(
            in_channels=node_features,
            out_channels=hidden_channels,
            edge_dim=hidden_channels,  # Updated edge features from line graph
            dropout=dropout
        )

        # Edge feature update
        self.edge_update = nn.Sequential(
            nn.Linear(edge_features + hidden_channels, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # Node feature update
        self.node_update = nn.Sequential(
            nn.Linear(node_features + hidden_channels, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # Layer normalization
        self.node_norm = nn.LayerNorm(hidden_channels)
        self.edge_norm = nn.LayerNorm(hidden_channels)

    def forward(self, atomic_graph_data, line_graph_data):
        """Forward pass of ALIGNN convolution."""
        # Extract data
        x_atomic = atomic_graph_data.x
        edge_index_atomic = atomic_graph_data.edge_index
        edge_attr_atomic = atomic_graph_data.edge_attr

        x_line = line_graph_data.x
        edge_index_line = line_graph_data.edge_index
        edge_attr_line = line_graph_data.edge_attr

        # Step 1: Update line graph (three-body interactions)
        if edge_index_line.size(1) > 0:
            x_line_updated = self.line_graph_conv(x_line, edge_index_line, edge_attr_line)
            x_line_updated = self.edge_norm(x_line_updated)
        else:
            # Handle case with no line graph edges
            x_line_updated = torch.zeros(x_line.size(0), self.hidden_channels,
                                       device=x_line.device, dtype=x_line.dtype)

        # Step 2: Update edge features in atomic graph using line graph information
        if x_line.size(0) > 0:
            # Combine original edge features with line graph updates
            edge_attr_combined = torch.cat([edge_attr_atomic, x_line_updated], dim=-1)
            edge_attr_updated = self.edge_update(edge_attr_combined)
        else:
            # Fallback if no line graph nodes
            edge_attr_updated = self.edge_update(
                torch.cat([edge_attr_atomic,
                          torch.zeros(edge_attr_atomic.size(0), self.hidden_channels,
                                    device=edge_attr_atomic.device)], dim=-1)
            )

        edge_attr_updated = self.edge_norm(edge_attr_updated)

        # Step 3: Update atomic graph (two-body interactions)
        x_atomic_updated = self.atomic_graph_conv(
            x_atomic, edge_index_atomic, edge_attr_updated
        )
        x_atomic_updated = self.node_norm(x_atomic_updated)

        # Step 4: Update node features
        x_atomic_combined = torch.cat([x_atomic, x_atomic_updated], dim=-1)
        x_atomic_final = self.node_update(x_atomic_combined)

        # Create updated data objects
        updated_atomic_graph = Data(
            x=x_atomic_final,
            edge_index=edge_index_atomic,
            edge_attr=edge_attr_updated,
            pos=atomic_graph_data.pos if hasattr(atomic_graph_data, 'pos') else None,
            num_nodes=atomic_graph_data.num_nodes
        )

        updated_line_graph = Data(
            x=x_line_updated,
            edge_index=edge_index_line,
            edge_attr=edge_attr_line,
            num_nodes=line_graph_data.num_nodes
        )

        return updated_atomic_graph, updated_line_graph


class ALIGNN(nn.Module):
    """Atomistic Line Graph Neural Network (ALIGNN) for crystal property prediction."""

    def __init__(self, node_features: int, edge_features: int,
                 hidden_channels: int = 128, num_layers: int = 4,
                 num_classes: int = 1, dropout: float = 0.1,
                 pooling: str = 'mean', use_batch_norm: bool = True):
        super().__init__()

        self.node_features = node_features
        self.edge_features = edge_features
        self.hidden_channels = hidden_channels
        self.num_layers = num_layers
        self.num_classes = num_classes
        self.dropout = dropout
        self.pooling = pooling
        self.use_batch_norm = use_batch_norm

        # Input embeddings
        self.node_embedding = nn.Sequential(
            nn.Linear(node_features, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        self.edge_embedding = nn.Sequential(
            nn.Linear(edge_features, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # ALIGNN convolution layers
        self.alignn_layers = nn.ModuleList([
            ALIGNNConv(
                node_features=hidden_channels if i > 0 else hidden_channels,
                edge_features=hidden_channels if i > 0 else hidden_channels,
                hidden_channels=hidden_channels,
                dropout=dropout
            )
            for i in range(num_layers)
        ])

        # Batch normalization layers
        if use_batch_norm:
            self.node_batch_norms = nn.ModuleList([
                nn.BatchNorm1d(hidden_channels) for _ in range(num_layers)
            ])
            self.edge_batch_norms = nn.ModuleList([
                nn.BatchNorm1d(hidden_channels) for _ in range(num_layers)
            ])

        # Global pooling
        if pooling == 'mean':
            self.pool = global_mean_pool
        else:
            raise ValueError(f"Unknown pooling method: {pooling}")

        # Output layers
        self.classifier = nn.Sequential(
            nn.Linear(hidden_channels, hidden_channels // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_channels // 2, hidden_channels // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_channels // 4, num_classes)
        )

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.zeros_(module.bias)

    def forward(self, batch_data: Dict[str, Any]) -> torch.Tensor:
        """Forward pass of ALIGNN model."""
        atomic_graph = batch_data['graph']
        line_graph = batch_data['line_graph']
        batch = batch_data.get('batch', None)

        # Handle batch indices
        if batch is None:
            batch = torch.zeros(atomic_graph.x.size(0), dtype=torch.long,
                              device=atomic_graph.x.device)

        # Initial embeddings
        x_atomic = self.node_embedding(atomic_graph.x)
        edge_attr_atomic = self.edge_embedding(atomic_graph.edge_attr)

        # Handle line graph features
        if line_graph.x.size(0) > 0:
            x_line = line_graph.x
            if x_line.size(1) != self.hidden_channels:
                # If line graph features don't match hidden channels, embed them
                line_embed = nn.Linear(x_line.size(1), self.hidden_channels).to(x_line.device)
                x_line = line_embed(x_line)
        else:
            # Create empty line graph features if no line graph
            x_line = torch.empty(0, self.hidden_channels, device=atomic_graph.x.device)

        # Update atomic graph data
        atomic_graph_updated = Data(
            x=x_atomic,
            edge_index=atomic_graph.edge_index,
            edge_attr=edge_attr_atomic,
            pos=atomic_graph.pos if hasattr(atomic_graph, 'pos') else None,
            num_nodes=atomic_graph.num_nodes
        )

        line_graph_updated = Data(
            x=x_line,
            edge_index=line_graph.edge_index,
            edge_attr=line_graph.edge_attr,
            num_nodes=line_graph.num_nodes
        )

        # Apply ALIGNN layers
        for i, alignn_layer in enumerate(self.alignn_layers):
            # Apply ALIGNN convolution
            atomic_graph_updated, line_graph_updated = alignn_layer(
                atomic_graph_updated, line_graph_updated
            )

            # Apply batch normalization if enabled
            if self.use_batch_norm:
                if atomic_graph_updated.x.size(0) > 0:
                    atomic_graph_updated.x = self.node_batch_norms[i](atomic_graph_updated.x)
                if line_graph_updated.x.size(0) > 0:
                    line_graph_updated.x = self.edge_batch_norms[i](line_graph_updated.x)

            # Apply activation and dropout
            atomic_graph_updated.x = F.relu(atomic_graph_updated.x)
            atomic_graph_updated.x = F.dropout(atomic_graph_updated.x,
                                             p=self.dropout, training=self.training)

        # Global pooling
        graph_representation = self.pool(atomic_graph_updated.x, batch)

        # Final prediction
        output = self.classifier(graph_representation)

        return output


# ============================================================================
# TRAINING UTILITIES
# ============================================================================

def collate_alignn_batch(batch: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Collate function for ALIGNN data loader."""
    # Separate atomic graphs and line graphs
    atomic_graphs = [sample['graph'] for sample in batch]
    line_graphs = [sample['line_graph'] for sample in batch]
    targets = [sample['target'] for sample in batch]
    material_ids = [sample['material_id'] for sample in batch]

    # Batch atomic graphs
    batched_atomic = Batch.from_data_list(atomic_graphs)

    # Batch line graphs (handle empty graphs)
    non_empty_graphs = [g for g in line_graphs if g.num_nodes > 0]
    if non_empty_graphs:
        batched_line = Batch.from_data_list(non_empty_graphs)
    else:
        # Create empty batch
        empty_graph = Data(
            x=torch.empty(0, 4), # Assuming 4 edge features for line graph
            edge_index=torch.empty(2, 0, dtype=torch.long),
            edge_attr=torch.empty(0, 4),
            num_nodes=0
        )
        batched_line = Batch.from_data_list([empty_graph])


    # Stack targets
    batched_targets = torch.stack(targets, dim=0)

    return {
        'graph': batched_atomic,
        'line_graph': batched_line,
        'target': batched_targets,
        'material_ids': material_ids,
        'batch': batched_atomic.batch
    }


def move_batch_to_device(batch: Dict[str, Any], device: torch.device) -> Dict[str, Any]:
    """Move batch data to specified device."""
    moved_batch = {}
    for key, value in batch.items():
        if key == 'material_ids':
            moved_batch[key] = value  # Keep strings on CPU
        elif hasattr(value, 'to'):
            moved_batch[key] = value.to(device)
        else:
            moved_batch[key] = value
    return moved_batch


def denormalize_targets(normalized_targets: torch.Tensor, mean: float, std: float):
    """Denormalize target values."""
    return normalized_targets * std + mean


def train_alignn_with_epochs(model: nn.Module, train_loader: DataLoader, val_loader: DataLoader,
                            num_epochs: int, learning_rate: float = 1e-3, weight_decay: float = 1e-5,
                            device: torch.device = None, save_path: str = None,
                            target_mean: float = 0.0, target_std: float = 1.0,
                            patience: int = 20, min_delta: float = 1e-4) -> Dict[str, Any]:
    """
    Train ALIGNN model with detailed epoch tracking and loss display.

    Args:
        model: ALIGNN model to train
        train_loader: Training data loader
        val_loader: Validation data loader
        num_epochs: Number of training epochs
        learning_rate: Learning rate for optimizer
        weight_decay: Weight decay for regularization
        device: Device to train on
        save_path: Path to save best model
        target_mean: Mean of targets for denormalization
        target_std: Standard deviation of targets for denormalization
        patience: Early stopping patience
        min_delta: Minimum improvement for early stopping

    Returns:
        Training history dictionary
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    model = model.to(device)

    # Setup optimizer and scheduler
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=10, verbose=True
    )

    # Loss function
    criterion = nn.MSELoss()

    # Training history
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_mae': [],
        'val_mae': [],
        'learning_rates': [],
        'epochs': []
    }

    best_val_loss = float('inf')
    epochs_without_improvement = 0

    print(f"Starting ALIGNN training on {device}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    print(f"Training for {num_epochs} epochs")
    print("=" * 80)

    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_losses = []
        train_maes = []

        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1:3d}/{num_epochs} [Train]",
                         leave=False, ncols=100)
        for batch in train_pbar:
            optimizer.zero_grad()

            # Move batch to device
            batch = move_batch_to_device(batch, device)

            # Forward pass
            predictions = model(batch)
            targets = batch['target']

            # Compute loss
            loss = criterion(predictions, targets)

            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            # Compute metrics
            with torch.no_grad():
                # Denormalize for MAE calculation
                pred_denorm = denormalize_targets(predictions, target_mean, target_std)
                target_denorm = denormalize_targets(targets, target_mean, target_std)
                mae = torch.mean(torch.abs(pred_denorm - target_denorm))

                train_losses.append(loss.item())
                train_maes.append(mae.item())

            train_pbar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'mae': f"{mae.item():.4f}"
            })

        # Validation phase
        model.eval()
        val_losses = []
        val_maes = []

        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1:3d}/{num_epochs} [Val]",
                           leave=False, ncols=100)
            for batch in val_pbar:
                batch = move_batch_to_device(batch, device)

                predictions = model(batch)
                targets = batch['target']

                loss = criterion(predictions, targets)

                # Denormalize for MAE calculation
                pred_denorm = denormalize_targets(predictions, target_mean, target_std)
                target_denorm = denormalize_targets(targets, target_mean, target_std)
                mae = torch.mean(torch.abs(pred_denorm - target_denorm))

                val_losses.append(loss.item())
                val_maes.append(mae.item())

                val_pbar.set_postfix({
                    'loss': f"{loss.item():.4f}",
                    'mae': f"{mae.item():.4f}"
                })

        # Calculate epoch metrics
        epoch_train_loss = np.mean(train_losses)
        epoch_val_loss = np.mean(val_losses)
        epoch_train_mae = np.mean(train_maes)
        epoch_val_mae = np.mean(val_maes)
        current_lr = optimizer.param_groups[0]['lr']

        # Update history
        history['epochs'].append(epoch + 1)
        history['train_loss'].append(epoch_train_loss)
        history['val_loss'].append(epoch_val_loss)
        history['train_mae'].append(epoch_train_mae)
        history['val_mae'].append(epoch_val_mae)
        history['learning_rates'].append(current_lr)

        # Learning rate scheduling
        scheduler.step(epoch_val_loss)

        # Print epoch results
        print(f"Epoch {epoch+1:3d}/{num_epochs} | "
              f"Train Loss: {epoch_train_loss:.4f} | Val Loss: {epoch_val_loss:.4f} | "
              f"Train MAE: {epoch_train_mae:.4f} | Val MAE: {epoch_val_mae:.4f} | "
              f"LR: {current_lr:.2e}")

        # Early stopping check
        if epoch_val_loss < best_val_loss - min_delta:
            best_val_loss = epoch_val_loss
            epochs_without_improvement = 0

            # Save best model
            if save_path:
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'epoch': epoch,
                    'val_loss': epoch_val_loss,
                    'target_mean': target_mean,
                    'target_std': target_std,
                    'history': history
                }, save_path)
                print(f"    → New best model saved (Val Loss: {epoch_val_loss:.4f})")
        else:
            epochs_without_improvement += 1

        # Early stopping
        if epochs_without_improvement >= patience:
            print(f"\nEarly stopping after {epoch+1} epochs (no improvement for {patience} epochs)")
            break

        print("-" * 80)

    print(f"\nTraining completed!")
    print(f"Best validation loss: {best_val_loss:.4f}")

    return history


def evaluate_alignn(model: nn.Module, data_loader: DataLoader, device: torch.device,
                   target_mean: float = 0.0, target_std: float = 1.0) -> Dict[str, Any]:
    """Evaluate ALIGNN model on a dataset."""
    model.eval()
    model = model.to(device)

    all_predictions = []
    all_targets = []
    total_loss = 0.0
    num_batches = 0

    criterion = nn.MSELoss()

    with torch.no_grad():
        pbar = tqdm(data_loader, desc="Evaluating", ncols=100)
        for batch in pbar:
            batch = move_batch_to_device(batch, device)

            # Forward pass
            predictions = model(batch)
            targets = batch['target']

            # Compute loss
            loss = criterion(predictions, targets)
            total_loss += loss.item()
            num_batches += 1

            # Denormalize predictions and targets
            pred_denorm = denormalize_targets(predictions, target_mean, target_std)
            target_denorm = denormalize_targets(targets, target_mean, target_std)

            # Store results
            all_predictions.extend(pred_denorm.cpu().numpy().flatten())
            all_targets.extend(target_denorm.cpu().numpy().flatten())

            # Update progress bar
            mae = np.mean(np.abs(np.array(all_predictions) - np.array(all_targets)))
            pbar.set_postfix({'MAE': f"{mae:.4f}"})

    # Convert to numpy arrays
    predictions = np.array(all_predictions)
    targets = np.array(all_targets)

    # Compute metrics
    mae = mean_absolute_error(targets, predictions)
    mse = mean_squared_error(targets, predictions)
    rmse = np.sqrt(mse)
    r2 = r2_score(targets, predictions)

    # Additional metrics
    mape = np.mean(np.abs((targets - predictions) / (targets + 1e-8))) * 100
    max_error = np.max(np.abs(targets - predictions))

    metrics = {
        'mae': mae,
        'mse': mse,
        'rmse': rmse,
        'r2': r2,
        'mape': mape,
        'max_error': max_error,
        'avg_loss': total_loss / num_batches,
        'num_samples': len(predictions)
    }

    return metrics


def plot_training_history(history: Dict[str, Any], save_path: str = None):
    """Plot training history with epoch tracking."""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('ALIGNN Training History', fontsize=16)

    epochs = history['epochs']

    # Training and validation loss
    ax1 = axes[0, 0]
    ax1.plot(epochs, history['train_loss'], 'b-', label='Training Loss', linewidth=2)
    ax1.plot(epochs, history['val_loss'], 'r-', label='Validation Loss', linewidth=2)
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.set_title('Training and Validation Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Training and validation MAE
    ax2 = axes[0, 1]
    ax2.plot(epochs, history['train_mae'], 'b-', label='Training MAE', linewidth=2)
    ax2.plot(epochs, history['val_mae'], 'r-', label='Validation MAE', linewidth=2)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('MAE')
    ax2.set_title('Training and Validation MAE')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # Learning rate
    ax3 = axes[1, 0]
    ax3.plot(epochs, history['learning_rates'], 'g-', linewidth=2)
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Learning Rate')
    ax3.set_title('Learning Rate Schedule')
    ax3.set_yscale('log')
    ax3.grid(True, alpha=0.3)

    # Loss comparison
    ax4 = axes[1, 1]
    ax4.plot(epochs, np.array(history['val_loss']) - np.array(history['train_loss']),
             'purple', linewidth=2, label='Val - Train Loss')
    ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax4.set_xlabel('Epoch')
    ax4.set_ylabel('Loss Difference')
    ax4.set_title('Overfitting Monitor (Val - Train Loss)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Training history plot saved to {save_path}")

    plt.show()


# ============================================================================
# MAIN TRAINING PIPELINE
# ============================================================================

def run_complete_alignn_pipeline(dataset_path: str, target_name: str, output_dir: str,
                                num_epochs: int = 200, batch_size: int = 32,
                                learning_rate: float = 1e-3, hidden_channels: int = 128,
                                num_layers: int = 4, device: str = 'auto', seed: int = 42) -> Dict[str, Any]:
    """
    Run complete ALIGNN training pipeline with epoch tracking.

    Args:
        dataset_path: Path to dataset directory
        target_name: Name of target property to predict
        output_dir: Directory to save results
        num_epochs: Number of training epochs
        batch_size: Batch size for training
        learning_rate: Learning rate for optimizer
        hidden_channels: Number of hidden channels in model
        num_layers: Number of ALIGNN layers
        device: Device to use ('auto', 'cpu', 'cuda')
        seed: Random seed for reproducibility

    Returns:
        Dictionary containing results and metrics
    """
    # Set random seeds for reproducibility
    torch.manual_seed(seed)
    np.random.seed(seed)

    # Setup output directory
    os.makedirs(output_dir, exist_ok=True)

    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(output_dir, 'training.log')),
            logging.StreamHandler()
        ]
    )


    # Setup device
    if device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device)

    print(f"Using device: {device}")

    # Load dataset
    print("Loading dataset...")
    dataset = ALIGNNDataset(
        path=dataset_path,
        target_name=target_name,
        cutoff=8.0,
        add_self_loops=True
    )

    print(f"Dataset loaded: {len(dataset)} samples")

    # Get dataset statistics
    dataset_stats = dataset.get_statistics()
    print(f"Target statistics: mean={dataset_stats['target_mean']:.4f}, "
          f"std={dataset_stats['target_std']:.4f}")

    # Split dataset
    train_size = int(0.8 * len(dataset))
    val_size = int(0.1 * len(dataset))
    test_size = len(dataset) - train_size - val_size

    train_dataset, val_dataset, test_dataset = random_split(
        dataset, [train_size, val_size, test_size],
        generator=torch.Generator().manual_seed(seed) # Use the provided seed
    )

    print(f"Dataset split: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")

    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        collate_fn=collate_alignn_batch,
        num_workers=0 # Set num_workers to 0 for Colab compatibility
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=collate_alignn_batch,
        num_workers=0 # Set num_workers to 0 for Colab compatibility
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        collate_fn=collate_alignn_batch,
        num_workers=0 # Set num_workers to 0 for Colab compatibility
    )

    # Get sample to determine input dimensions
    sample = dataset[0]
    node_features = sample['graph'].x.size(1)
    edge_features = sample['graph'].edge_attr.size(1)

    print(f"Input dimensions: nodes={node_features}, edges={edge_features}")

    # Create model
    model = ALIGNN(
        node_features=node_features,
        edge_features=edge_features,
        hidden_channels=hidden_channels,
        num_layers=num_layers,
        num_classes=1,
        dropout=0.1,
        pooling='mean',
        use_batch_norm=True
    )

    print(f"Model created with {sum(p.numel() for p in model.parameters()):,} parameters")

    # Normalize targets
    target_mean = dataset_stats['target_mean']
    target_std = dataset_stats['target_std']

    # Train model
    print("\nStarting training...")
    history = train_alignn_with_epochs(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=num_epochs,
        learning_rate=learning_rate,
        weight_decay=1e-5,
        device=device,
        save_path=os.path.join(output_dir, 'best_model.pt'),
        target_mean=target_mean,
        target_std=target_std,
        patience=20,
        min_delta=1e-4
    )

    # Plot training history
    plot_training_history(history, save_path=os.path.join(output_dir, 'training_history.png'))

    # Load best model for evaluation
    checkpoint = torch.load(os.path.join(output_dir, 'best_model.pt'), map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])

    # Evaluate on test set
    print("\nEvaluating on test set...")
    test_results = evaluate_alignn(
        model=model,
        data_loader=test_loader,
        device=device,
        target_mean=target_mean,
        target_std=target_std
    )

    # Print final results
    print("\n" + "="*60)
    print("ALIGNN TRAINING COMPLETED")
    print("="*60)
    print(f"Final Test Results:")
    print(f"  MAE: {test_results['mae']:.4f}")
    print(f"  RMSE: {test_results['rmse']:.4f}")
    print(f"  R²: {test_results['r2']:.4f}")
    print(f"  MAPE: {test_results['mape']:.2f}%")
    print(f"  Max Error: {test_results['max_error']:.4f}")

    # Save results
    results = {
        'dataset_stats': dataset_stats,
        'model_config': {
            'node_features': node_features,
            'edge_features': edge_features,
            'hidden_channels': hidden_channels,
            'num_layers': num_layers,
            'num_parameters': sum(p.numel() for p in model.parameters())
        },
        'training_config': {
            'num_epochs': num_epochs,
            'batch_size': batch_size,
            'learning_rate': learning_rate,
            'target_mean': target_mean,
            'target_std': target_std
        },
        'training_history': history,
        'test_metrics': test_results
    }

    # Save results as JSON
    with open(os.path.join(output_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)

    print(f"\nResults saved to {output_dir}")

    return results


# ============================================================================
# MAIN EXECUTION BLOCK FOR COLAB
# ============================================================================

# Define parameters directly
dataset_path = '/content/gdrive/MyDrive/2024SUMMER/MaterialsProjectData/MaterialsProjectData/20KSPLIT/' # Replace with your dataset path
target_name = 'band_gap' # Replace with your target property name
output_dir = 'alignn_bandgap_results' # Output directory

# Run the pipeline
print("="*80)
print("ALIGNN (Atomistic Line Graph Neural Network) Training Pipeline")
print("Complete implementation with epoch tracking and loss display")
print("Running directly in Colab...")
print("="*80)

try:
    results = run_complete_alignn_pipeline(
        dataset_path=dataset_path,
        target_name=target_name,
        output_dir=output_dir,
        num_epochs=200, # Example value
        batch_size=32, # Example value
        learning_rate=1e-3, # Example value
        hidden_channels=128, # Example value
        num_layers=4, # Example value
        device='auto', # Use 'auto' to detect GPU
        seed=42 # Example seed
    )

    print("\n🎉 ALIGNN training completed successfully!")

except Exception as e:
    print(f"\n❌ Error during training: {str(e)}")
    # Optionally log the full traceback
    # import traceback
    # logging.error(f"Training failed: {str(e)}\n{traceback.format_exc()}")