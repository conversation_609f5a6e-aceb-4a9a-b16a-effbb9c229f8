"""
Training utilities for ALIGNN model.

This module provides training functions and utilities for the ALIGNN baseline model.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import logging
from typing import Dict, Any, Tuple, Optional

from .data_utils import collate_alignn_batch, normalize_targets, denormalize_targets


def train_alignn(model: nn.Module, train_loader: DataLoader, val_loader: DataLoader,
                 num_epochs: int, learning_rate: float = 1e-3, weight_decay: float = 1e-5,
                 device: torch.device = None, save_path: str = None,
                 target_mean: float = 0.0, target_std: float = 1.0,
                 patience: int = 20, min_delta: float = 1e-4) -> Dict[str, Any]:
    """
    Train ALIGNN model.
    
    Args:
        model: ALIGNN model to train
        train_loader: Training data loader
        val_loader: Validation data loader
        num_epochs: Number of training epochs
        learning_rate: Learning rate for optimizer
        weight_decay: Weight decay for regularization
        device: Device to train on
        save_path: Path to save best model
        target_mean: Mean of targets for denormalization
        target_std: Standard deviation of targets for denormalization
        patience: Early stopping patience
        min_delta: Minimum improvement for early stopping
        
    Returns:
        Training history dictionary
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model = model.to(device)
    
    # Setup optimizer and scheduler
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=10, verbose=True
    )
    
    # Loss function
    criterion = nn.MSELoss()
    
    # Training history
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_mae': [],
        'val_mae': [],
        'learning_rates': []
    }
    
    best_val_loss = float('inf')
    epochs_without_improvement = 0
    
    logging.info(f"Starting training on {device}")
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_losses = []
        train_maes = []
        
        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Train]")
        for batch in train_pbar:
            optimizer.zero_grad()
            
            # Move batch to device
            batch = move_batch_to_device(batch, device)
            
            # Forward pass
            predictions = model(batch)
            targets = batch['target']
            
            # Compute loss
            loss = criterion(predictions, targets)
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            # Compute metrics
            with torch.no_grad():
                # Denormalize for MAE calculation
                pred_denorm = denormalize_targets(predictions, target_mean, target_std)
                target_denorm = denormalize_targets(targets, target_mean, target_std)
                mae = torch.mean(torch.abs(pred_denorm - target_denorm))
                
                train_losses.append(loss.item())
                train_maes.append(mae.item())
            
            train_pbar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'mae': f"{mae.item():.4f}"
            })
        
        # Validation phase
        model.eval()
        val_losses = []
        val_maes = []
        
        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Val]")
            for batch in val_pbar:
                batch = move_batch_to_device(batch, device)
                
                predictions = model(batch)
                targets = batch['target']
                
                loss = criterion(predictions, targets)
                
                # Denormalize for MAE calculation
                pred_denorm = denormalize_targets(predictions, target_mean, target_std)
                target_denorm = denormalize_targets(targets, target_mean, target_std)
                mae = torch.mean(torch.abs(pred_denorm - target_denorm))
                
                val_losses.append(loss.item())
                val_maes.append(mae.item())
                
                val_pbar.set_postfix({
                    'loss': f"{loss.item():.4f}",
                    'mae': f"{mae.item():.4f}"
                })
        
        # Calculate epoch metrics
        epoch_train_loss = np.mean(train_losses)
        epoch_val_loss = np.mean(val_losses)
        epoch_train_mae = np.mean(train_maes)
        epoch_val_mae = np.mean(val_maes)
        current_lr = optimizer.param_groups[0]['lr']
        
        # Update history
        history['train_loss'].append(epoch_train_loss)
        history['val_loss'].append(epoch_val_loss)
        history['train_mae'].append(epoch_train_mae)
        history['val_mae'].append(epoch_val_mae)
        history['learning_rates'].append(current_lr)
        
        # Learning rate scheduling
        scheduler.step(epoch_val_loss)
        
        # Early stopping check
        if epoch_val_loss < best_val_loss - min_delta:
            best_val_loss = epoch_val_loss
            epochs_without_improvement = 0
            
            # Save best model
            if save_path:
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'epoch': epoch,
                    'val_loss': epoch_val_loss,
                    'target_mean': target_mean,
                    'target_std': target_std,
                    'history': history
                }, save_path)
        else:
            epochs_without_improvement += 1
        
        # Log epoch results
        logging.info(
            f"Epoch {epoch+1}/{num_epochs}: "
            f"Train Loss: {epoch_train_loss:.4f}, Val Loss: {epoch_val_loss:.4f}, "
            f"Train MAE: {epoch_train_mae:.4f}, Val MAE: {epoch_val_mae:.4f}, "
            f"LR: {current_lr:.2e}"
        )
        
        # Early stopping
        if epochs_without_improvement >= patience:
            logging.info(f"Early stopping after {epoch+1} epochs")
            break
    
    return history


def move_batch_to_device(batch: Dict[str, Any], device: torch.device) -> Dict[str, Any]:
    """Move batch data to specified device."""
    moved_batch = {}
    for key, value in batch.items():
        if key == 'material_ids':
            moved_batch[key] = value  # Keep strings on CPU
        elif hasattr(value, 'to'):
            moved_batch[key] = value.to(device)
        else:
            moved_batch[key] = value
    return moved_batch


def setup_logging(log_file: str = None, level: int = logging.INFO):
    """Setup logging configuration."""
    handlers = [logging.StreamHandler()]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=handlers
    )


def save_training_checkpoint(model: nn.Module, optimizer: optim.Optimizer,
                           epoch: int, loss: float, path: str,
                           additional_info: Dict[str, Any] = None):
    """Save training checkpoint."""
    checkpoint = {
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'epoch': epoch,
        'loss': loss
    }
    
    if additional_info:
        checkpoint.update(additional_info)
    
    torch.save(checkpoint, path)


def load_training_checkpoint(model: nn.Module, optimizer: optim.Optimizer,
                           path: str, device: torch.device) -> Dict[str, Any]:
    """Load training checkpoint."""
    checkpoint = torch.load(path, map_location=device)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    return checkpoint
