#!/usr/bin/env python3
"""
Basic usage example for CGN-E3.

This script demonstrates how to use the CGN-E3 library for training
and evaluating models on crystal property prediction tasks.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import torch
from torch.utils.data import DataLoader, random_split
from torch_geometric.data import Batch

from cgn_e3.data import CartesianGraphDataset
from cgn_e3.models import E3EquivariantCrystalGNNCapsNet
from cgn_e3.utils.training import train_spatial_gnn
from cgn_e3.utils.evaluation import evaluate_spatial_gnn


def main():
    """Basic usage example."""
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Example dataset path (replace with your actual path)
    dataset_path = "path/to/your/dataset"
    target_name = "e_form"  # or "band_gap", etc.
    
    try:
        # Load dataset
        print("Loading dataset...")
        dataset = CartesianGraphDataset(dataset_path, target_name=target_name)
        print(f"Dataset loaded with {len(dataset)} samples")
        
        # Split dataset
        train_size = int(0.8 * len(dataset))
        test_size = len(dataset) - train_size
        train_dataset, test_dataset = random_split(
            dataset, [train_size, test_size],
            generator=torch.Generator().manual_seed(42)
        )
        
        # Create data loaders
        batch_size = 32
        train_loader = DataLoader(
            train_dataset, batch_size=batch_size, 
            shuffle=True, collate_fn=Batch.from_data_list
        )
        test_loader = DataLoader(
            test_dataset, batch_size=batch_size, 
            collate_fn=Batch.from_data_list
        )
        
        # Initialize model
        print("Initializing model...")
        model = E3EquivariantCrystalGNNCapsNet(
            node_features=dataset[0].x.size(1),
            edge_features=dataset[0].edge_attr.size(1),
            hidden_channels=128,  # Smaller for example
            num_conv_layers=2,
            primary_caps=4,
            primary_dim=8,
            secondary_caps=3,
            secondary_dim=8,
            dropout_rate=0.1
        ).to(device)
        
        print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # Set up optimizer
        optimizer = torch.optim.AdamW(
            model.parameters(), lr=1e-3, weight_decay=1e-4
        )
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=10, verbose=True
        )
        
        # Train model
        print("Training model...")
        trained_model, train_losses, val_losses, metrics = train_spatial_gnn(
            model, train_loader, optimizer, device,
            epochs=50,  # Fewer epochs for example
            scheduler=scheduler,
            early_stopping_patience=20,
            checkpoint_path="example_model.pt",
            metrics_path="example_metrics.csv"
        )
        
        # Evaluate on test set
        print("Evaluating model...")
        results = evaluate_spatial_gnn(
            trained_model, test_loader, device, 
            results_path="example_test_results.csv"
        )
        
        # Print results
        print("\nFinal Results:")
        print(f"MSE:  {results['mse']:.6f}")
        print(f"RMSE: {results['rmse']:.6f}")
        print(f"MAE:  {results['mae']:.6f}")
        print(f"R²:   {results['r2']:.6f}")
        
    except FileNotFoundError:
        print(f"Dataset not found at {dataset_path}")
        print("Please update the dataset_path variable with the correct path to your dataset.")
        print("\nDataset should contain:")
        print("- BandgapTargets.npz (graph data)")
        print("- BandgapTargets_config.json (configuration)")
        print("- BandgapTargets.csv (target values)")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have the required dependencies installed:")
        print("pip install torch torch-geometric e3nn numpy pandas scikit-learn")


if __name__ == "__main__":
    main()
