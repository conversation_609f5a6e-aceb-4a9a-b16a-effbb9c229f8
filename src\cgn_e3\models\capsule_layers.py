"""
E(3)-equivariant capsule network layers.

This module contains capsule network layers that maintain E(3) equivariance
while performing dynamic routing between capsules.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from e3nn import o3
from e3nn.o3 import FullyConnectedTensorProduct


class E3EquivariantPrimaryCapsuleLayer(nn.Module):
    """
    E(3)-equivariant primary capsule layer.
    
    Converts scalar and vector features into capsule representations
    while maintaining E(3) equivariance.
    
    Args:
        scalar_features (int): Number of input scalar features
        vector_features (int): Number of input vector features
        out_caps (int): Number of output capsules
        caps_dim (int): Dimension of each capsule
    """
    
    def __init__(self, scalar_features, vector_features, out_caps, caps_dim):
        super().__init__()
        self.out_caps = out_caps
        self.caps_dim = caps_dim

        # Scalar projection (invariant)
        self.scalar_projection = nn.Linear(
            scalar_features, out_caps * (caps_dim // 2)
        )

        # Vector projection weights (invariant)
        self.vector_weights = nn.Linear(scalar_features, out_caps)

        # Equivariant vector transformation
        irreps_in1 = o3.Irreps(f"{vector_features}x1e")  # Input vectors
        irreps_in2 = o3.Irreps("0e")  # Scalar input for invariant output
        irreps_out = o3.Irreps(f"{caps_dim//2}x0e")  # Output scalars

        # Use tensor product to create invariants from vectors
        self.vector_tp = FullyConnectedTensorProduct(
            irreps_in1=irreps_in1,
            irreps_in2=irreps_in2,
            irreps_out=irreps_out,
            internal_weights=True
        )

    def forward(self, x_scalar, x_vector):
        """
        Forward pass of the primary capsule layer.
        
        Args:
            x_scalar (torch.Tensor): Scalar features [batch_size, scalar_features]
            x_vector (torch.Tensor): Vector features [batch_size, vector_features, 3]
            
        Returns:
            tuple: (capsules, x_vector) where capsules has shape 
                   [batch_size, out_caps, caps_dim]
        """
        batch_size = x_scalar.size(0)

        # Project scalar features (invariant)
        scalar_out = self.scalar_projection(x_scalar)
        scalar_out = scalar_out.view(batch_size, self.out_caps, self.caps_dim // 2)

        # Get weights for vector features (invariant)
        vector_weights = self.vector_weights(x_scalar)
        vector_weights = vector_weights.view(batch_size, self.out_caps, 1)

        # Create equivariant capsules
        vector_caps = []

        # Process vectors equivariantly for each capsule
        for i in range(self.out_caps):
            # Weight vectors (equivariant operation)
            weighted_vectors = vector_weights[:, i:i+1, :] * x_vector

            # Create invariants from vectors using tensor product
            # Reshape for tensor product
            weighted_vectors_flat = weighted_vectors.reshape(batch_size, -1)

            # Apply tensor product to get invariants
            invariants = self.vector_tp(
                weighted_vectors_flat, 
                torch.ones(batch_size, 1, device=x_scalar.device)
            )

            # Reshape to capsule dimension
            invariants = invariants.view(batch_size, self.caps_dim // 2)
            vector_caps.append(invariants)

        # Stack to get [batch_size, out_caps, caps_dim//2]
        vector_caps = torch.stack(vector_caps, dim=1)

        # Concatenate scalar and vector parts
        capsules = torch.cat([scalar_out, vector_caps], dim=2)

        return capsules, x_vector


class E3EquivariantSecondaryCapsuleLayer(nn.Module):
    """
    E(3)-equivariant secondary capsule layer with dynamic routing.
    
    Performs dynamic routing between primary and secondary capsules
    while maintaining E(3) equivariance.
    
    Args:
        in_dim (int): Input capsule dimension
        out_caps (int): Number of output capsules
        out_dim (int): Output capsule dimension
        routing_iterations (int): Number of routing iterations
    """
    
    def __init__(self, in_dim, out_caps, out_dim, routing_iterations=2):
        super().__init__()
        self.out_caps = out_caps
        self.out_dim = out_dim
        self.routing_iterations = routing_iterations
        self.scalar_dim = out_dim // 2
        self.vector_dim = out_dim // 2
        self.in_dim = in_dim

        # Transform matrices for scalar parts (invariant)
        self.W_scalar = nn.Parameter(
            torch.randn(out_caps, in_dim // 2, self.scalar_dim)
        )

        # Transform matrices for vector parts (invariant)
        self.W_vector = nn.Parameter(
            torch.randn(out_caps, in_dim // 2, self.vector_dim)
        )

        self.bias = nn.Parameter(torch.zeros(out_caps, out_dim))

    def squash(self, tensor, dim=-1):
        """
        Squash activation function for capsules.
        
        Args:
            tensor (torch.Tensor): Input tensor
            dim (int): Dimension to squash along
            
        Returns:
            torch.Tensor: Squashed tensor
        """
        squared_norm = (tensor ** 2).sum(dim=dim, keepdim=True)
        scale = squared_norm / (1 + squared_norm)
        return scale * tensor / (torch.sqrt(squared_norm) + 1e-8)

    def forward(self, x, x_vectors, batch):
        """
        Forward pass with dynamic routing.
        
        Args:
            x (torch.Tensor): Primary capsules [num_nodes, primary_caps, primary_dim]
            x_vectors (torch.Tensor): Vector features [num_nodes, channels, 3]
            batch (torch.Tensor): Batch indices for nodes
            
        Returns:
            tuple: (secondary_capsules, secondary_vectors)
        """
        batch_size = batch.max().item() + 1
        secondary_capsules = []
        secondary_vectors = []

        for b in range(batch_size):
            mask = (batch == b)
            if mask.sum() == 0:
                continue

            x_b = x[mask]  # [nodes, primary_caps, primary_dim]
            x_vectors_b = x_vectors[mask] if x_vectors is not None else None

            # Split input into scalar and vector parts
            nodes, primary_caps, primary_dim = x_b.size()
            scalar_part = x_b[:, :, :primary_dim//2]
            vector_part = x_b[:, :, primary_dim//2:]

            # Prepare prediction vectors for routing
            u_hat = torch.zeros(
                nodes, primary_caps, self.out_caps, self.out_dim, 
                device=x.device
            )

            # Transform features for each output capsule
            for i in range(self.out_caps):
                # Transform scalar part (invariant)
                scalar_transformed = torch.matmul(scalar_part, self.W_scalar[i])
                # Transform vector part (invariant)
                vector_transformed = torch.matmul(vector_part, self.W_vector[i])
                # Combine
                u_hat[:, :, i, :self.scalar_dim] = scalar_transformed
                u_hat[:, :, i, self.scalar_dim:] = vector_transformed

            # Flatten for routing
            u_hat_flat = u_hat.view(-1, self.out_caps, self.out_dim)
            num_inputs = u_hat_flat.size(0)

            # Initialize routing logits (invariant)
            b_ij = torch.zeros(num_inputs, self.out_caps, device=x.device)

            # Dynamic routing algorithm
            for iteration in range(self.routing_iterations):
                c_ij = F.softmax(b_ij, dim=1)
                c_ij = c_ij.unsqueeze(2)
                s_j = (c_ij * u_hat_flat).sum(dim=0) + self.bias
                v_j = self.squash(s_j, dim=1)
                
                if iteration < self.routing_iterations - 1:
                    u_scalar = u_hat_flat[:, :, :self.scalar_dim]
                    v_scalar = v_j[:, :self.scalar_dim].unsqueeze(0)
                    
                    # Calculate agreement (invariant)
                    agreement = (u_scalar * v_scalar).sum(dim=2)
                    b_ij = b_ij + agreement

            # Process equivariant vectors if provided
            if x_vectors_b is not None:
                # Reshape routing coefficients
                c_ij_reshaped = c_ij.view(nodes, primary_caps, self.out_caps, 1)

                # Get vector dimensions
                vector_channels = x_vectors_b.size(2)

                # Initialize routed vectors
                routed_vectors = torch.zeros(
                    self.out_caps, vector_channels, 3, device=x.device
                )

                # Vectorized aggregation (equivariant)
                for k in range(self.out_caps):
                    weights = c_ij_reshaped[:, :, k, 0].view(
                        nodes, primary_caps, 1, 1
                    )

                    # Handle different vector dimensions
                    if x_vectors_b.dim() == 3:  # [nodes, channels, 3]
                        expanded_vectors = x_vectors_b.unsqueeze(1).expand(
                            -1, primary_caps, -1, -1
                        )
                        routed_vectors[k] = (weights * expanded_vectors).sum(
                            dim=(0, 1)
                        )
                    else:  # [nodes, primary_caps, channels, 3]
                        routed_vectors[k] = (weights * x_vectors_b).sum(
                            dim=(0, 1)
                        )

                secondary_vectors.append(routed_vectors.unsqueeze(0))

            secondary_capsules.append(v_j.unsqueeze(0))

        if not secondary_capsules:
            return torch.zeros(
                (batch_size, self.out_caps, self.out_dim), device=x.device
            ), None

        return (
            torch.cat(secondary_capsules, dim=0),
            torch.cat(secondary_vectors, dim=0) if secondary_vectors else None
        )
