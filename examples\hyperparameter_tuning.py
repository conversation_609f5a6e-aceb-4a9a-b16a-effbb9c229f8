#!/usr/bin/env python3
"""
Hyperparameter tuning example for CGN-E3.

This script demonstrates how to perform hyperparameter tuning
using different model configurations.
"""

import sys
from pathlib import Path
import json
import itertools

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from cgn_e3.utils.pipeline import run_spatial_gnn_capsnet


def main():
    """Hyperparameter tuning example."""
    
    # Dataset configuration
    dataset_path = "path/to/your/dataset"
    target_name = "e_form"
    
    # Define hyperparameter grid
    param_grid = {
        'hidden_channels': [128, 256],
        'num_conv_layers': [2, 3],
        'primary_caps': [4, 8],
        'primary_dim': [8, 16],
        'secondary_caps': [3, 6],
        'secondary_dim': [8, 16],
        'dropout_rate': [0.01, 0.05, 0.1]
    }
    
    # Generate all combinations
    keys = param_grid.keys()
    values = param_grid.values()
    combinations = [dict(zip(keys, v)) for v in itertools.product(*values)]
    
    print(f"Testing {len(combinations)} hyperparameter combinations...")
    
    best_r2 = -float('inf')
    best_config = None
    results = []
    
    for i, config in enumerate(combinations):
        print(f"\n{'='*50}")
        print(f"Configuration {i+1}/{len(combinations)}")
        print(f"{'='*50}")
        
        for key, value in config.items():
            print(f"{key}: {value}")
        
        try:
            # Create output directory for this configuration
            output_dir = f"hyperparameter_search/config_{i+1}"
            
            # Run training
            model, test_results = run_spatial_gnn_capsnet(
                dataset_path=dataset_path,
                target_name=target_name,
                epochs=100,  # Reduced for faster search
                output_dir=output_dir,
                model_config=config
            )
            
            # Record results
            result = {
                'config_id': i + 1,
                'config': config,
                'mse': test_results['mse'],
                'rmse': test_results['rmse'],
                'mae': test_results['mae'],
                'r2': test_results['r2']
            }
            results.append(result)
            
            # Check if this is the best configuration
            if test_results['r2'] > best_r2:
                best_r2 = test_results['r2']
                best_config = config.copy()
                
                # Save best configuration
                with open('best_config.json', 'w') as f:
                    json.dump(best_config, f, indent=2)
            
            print(f"Results: R² = {test_results['r2']:.4f}, "
                  f"MAE = {test_results['mae']:.4f}")
            
        except Exception as e:
            print(f"Error with configuration {i+1}: {e}")
            result = {
                'config_id': i + 1,
                'config': config,
                'error': str(e)
            }
            results.append(result)
    
    # Save all results
    with open('hyperparameter_search_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Print summary
    print(f"\n{'='*50}")
    print("HYPERPARAMETER SEARCH SUMMARY")
    print(f"{'='*50}")
    
    successful_results = [r for r in results if 'r2' in r]
    if successful_results:
        successful_results.sort(key=lambda x: x['r2'], reverse=True)
        
        print(f"Best configuration (R² = {best_r2:.4f}):")
        for key, value in best_config.items():
            print(f"  {key}: {value}")
        
        print(f"\nTop 5 configurations:")
        for i, result in enumerate(successful_results[:5]):
            print(f"{i+1}. Config {result['config_id']}: "
                  f"R² = {result['r2']:.4f}, MAE = {result['mae']:.4f}")
    else:
        print("No successful configurations found.")
    
    print(f"\nDetailed results saved to: hyperparameter_search_results.json")
    print(f"Best configuration saved to: best_config.json")


if __name__ == "__main__":
    main()
