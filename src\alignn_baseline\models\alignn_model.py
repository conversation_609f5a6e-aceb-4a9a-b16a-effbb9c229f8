"""
Main ALIGNN model architecture.

This module implements the complete ALIGNN model that combines
line graph and atomic graph processing for crystal property prediction.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import global_mean_pool, global_max_pool, global_add_pool
from typing import Dict, Any

from .alignn_conv import ALIGNNConv


class ALIGNN(nn.Module):
    """
    Atomistic Line Graph Neural Network (ALIGNN) for crystal property prediction.
    
    ALIGNN processes both atomic graphs (representing two-body interactions)
    and line graphs (representing three-body interactions) to predict
    material properties.
    
    Args:
        node_features (int): Number of input node features
        edge_features (int): Number of input edge features
        hidden_channels (int): Hidden layer dimensions
        num_layers (int): Number of ALIGNN convolution layers
        num_classes (int): Number of output classes/properties
        dropout (float): Dropout rate
        pooling (str): Global pooling method ('mean', 'max', 'add')
        use_batch_norm (bool): Whether to use batch normalization
    """
    
    def __init__(self, node_features: int, edge_features: int, 
                 hidden_channels: int = 128, num_layers: int = 4,
                 num_classes: int = 1, dropout: float = 0.1,
                 pooling: str = 'mean', use_batch_norm: bool = True):
        super().__init__()
        
        self.node_features = node_features
        self.edge_features = edge_features
        self.hidden_channels = hidden_channels
        self.num_layers = num_layers
        self.num_classes = num_classes
        self.dropout = dropout
        self.pooling = pooling
        self.use_batch_norm = use_batch_norm
        
        # Input embeddings
        self.node_embedding = nn.Sequential(
            nn.Linear(node_features, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        self.edge_embedding = nn.Sequential(
            nn.Linear(edge_features, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # ALIGNN convolution layers
        self.alignn_layers = nn.ModuleList([
            ALIGNNConv(
                node_features=hidden_channels if i > 0 else hidden_channels,
                edge_features=hidden_channels if i > 0 else hidden_channels,
                hidden_channels=hidden_channels,
                dropout=dropout
            )
            for i in range(num_layers)
        ])
        
        # Batch normalization layers
        if use_batch_norm:
            self.node_batch_norms = nn.ModuleList([
                nn.BatchNorm1d(hidden_channels) for _ in range(num_layers)
            ])
            self.edge_batch_norms = nn.ModuleList([
                nn.BatchNorm1d(hidden_channels) for _ in range(num_layers)
            ])
        
        # Global pooling
        if pooling == 'mean':
            self.pool = global_mean_pool
        elif pooling == 'max':
            self.pool = global_max_pool
        elif pooling == 'add':
            self.pool = global_add_pool
        else:
            raise ValueError(f"Unknown pooling method: {pooling}")
        
        # Output layers
        self.classifier = nn.Sequential(
            nn.Linear(hidden_channels, hidden_channels // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_channels // 2, hidden_channels // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_channels // 4, num_classes)
        )
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
    
    def forward(self, batch_data: Dict[str, Any]) -> torch.Tensor:
        """
        Forward pass of ALIGNN model.
        
        Args:
            batch_data: Dictionary containing:
                - 'graph': Batched atomic graphs
                - 'line_graph': Batched line graphs
                - 'batch': Batch indices for atomic graphs
                - 'line_batch': Batch indices for line graphs (optional)
                
        Returns:
            Predicted property values [batch_size, num_classes]
        """
        atomic_graph = batch_data['graph']
        line_graph = batch_data['line_graph']
        batch = batch_data.get('batch', None)
        
        # Handle batch indices
        if batch is None:
            batch = torch.zeros(atomic_graph.x.size(0), dtype=torch.long, 
                              device=atomic_graph.x.device)
        
        # Initial embeddings
        x_atomic = self.node_embedding(atomic_graph.x)
        edge_attr_atomic = self.edge_embedding(atomic_graph.edge_attr)
        
        # Handle line graph features
        if line_graph.x.size(0) > 0:
            x_line = line_graph.x
            if x_line.size(1) != self.hidden_channels:
                # If line graph features don't match hidden channels, embed them
                line_embed = nn.Linear(x_line.size(1), self.hidden_channels).to(x_line.device)
                x_line = line_embed(x_line)
        else:
            # Create empty line graph features if no line graph
            x_line = torch.empty(0, self.hidden_channels, device=atomic_graph.x.device)
        
        # Update atomic graph data
        from torch_geometric.data import Data
        atomic_graph_updated = Data(
            x=x_atomic,
            edge_index=atomic_graph.edge_index,
            edge_attr=edge_attr_atomic,
            pos=atomic_graph.pos if hasattr(atomic_graph, 'pos') else None,
            num_nodes=atomic_graph.num_nodes
        )
        
        line_graph_updated = Data(
            x=x_line,
            edge_index=line_graph.edge_index,
            edge_attr=line_graph.edge_attr,
            num_nodes=line_graph.num_nodes
        )
        
        # Apply ALIGNN layers
        for i, alignn_layer in enumerate(self.alignn_layers):
            # Apply ALIGNN convolution
            atomic_graph_updated, line_graph_updated = alignn_layer(
                atomic_graph_updated, line_graph_updated
            )
            
            # Apply batch normalization if enabled
            if self.use_batch_norm:
                if atomic_graph_updated.x.size(0) > 0:
                    atomic_graph_updated.x = self.node_batch_norms[i](atomic_graph_updated.x)
                if line_graph_updated.x.size(0) > 0:
                    line_graph_updated.x = self.edge_batch_norms[i](line_graph_updated.x)
            
            # Apply activation and dropout
            atomic_graph_updated.x = F.relu(atomic_graph_updated.x)
            atomic_graph_updated.x = F.dropout(atomic_graph_updated.x, 
                                             p=self.dropout, training=self.training)
        
        # Global pooling
        graph_representation = self.pool(atomic_graph_updated.x, batch)
        
        # Final prediction
        output = self.classifier(graph_representation)
        
        return output
    
    def get_embeddings(self, batch_data: Dict[str, Any]) -> torch.Tensor:
        """
        Get graph embeddings without final classification layer.
        
        Args:
            batch_data: Dictionary containing graph data
            
        Returns:
            Graph embeddings [batch_size, hidden_channels]
        """
        atomic_graph = batch_data['graph']
        line_graph = batch_data['line_graph']
        batch = batch_data.get('batch', None)
        
        if batch is None:
            batch = torch.zeros(atomic_graph.x.size(0), dtype=torch.long, 
                              device=atomic_graph.x.device)
        
        # Forward pass without final classifier
        x_atomic = self.node_embedding(atomic_graph.x)
        edge_attr_atomic = self.edge_embedding(atomic_graph.edge_attr)
        
        # Handle line graph features
        if line_graph.x.size(0) > 0:
            x_line = line_graph.x
            if x_line.size(1) != self.hidden_channels:
                line_embed = nn.Linear(x_line.size(1), self.hidden_channels).to(x_line.device)
                x_line = line_embed(x_line)
        else:
            x_line = torch.empty(0, self.hidden_channels, device=atomic_graph.x.device)
        
        from torch_geometric.data import Data
        atomic_graph_updated = Data(
            x=x_atomic,
            edge_index=atomic_graph.edge_index,
            edge_attr=edge_attr_atomic,
            pos=atomic_graph.pos if hasattr(atomic_graph, 'pos') else None,
            num_nodes=atomic_graph.num_nodes
        )
        
        line_graph_updated = Data(
            x=x_line,
            edge_index=line_graph.edge_index,
            edge_attr=line_graph.edge_attr,
            num_nodes=line_graph.num_nodes
        )
        
        # Apply ALIGNN layers
        for i, alignn_layer in enumerate(self.alignn_layers):
            atomic_graph_updated, line_graph_updated = alignn_layer(
                atomic_graph_updated, line_graph_updated
            )
            
            if self.use_batch_norm:
                if atomic_graph_updated.x.size(0) > 0:
                    atomic_graph_updated.x = self.node_batch_norms[i](atomic_graph_updated.x)
            
            atomic_graph_updated.x = F.relu(atomic_graph_updated.x)
            atomic_graph_updated.x = F.dropout(atomic_graph_updated.x, 
                                             p=self.dropout, training=self.training)
        
        # Global pooling to get graph embeddings
        embeddings = self.pool(atomic_graph_updated.x, batch)
        
        return embeddings
