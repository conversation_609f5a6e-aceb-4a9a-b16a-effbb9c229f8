"""
E(3)-equivariant convolution layers for crystal graph networks.

This module contains convolution layers that respect E(3) symmetry,
including radial basis functions and equivariant message passing.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.utils import scatter
from e3nn import o3
from e3nn.o3 import FullyConnectedTensorProduct, spherical_harmonics


class RadialBasisLayer(nn.Module):
    """
    Radial basis function layer for encoding distances.
    
    Uses Gaussian radial basis functions with a smooth cutoff envelope
    to encode interatomic distances in a learnable way.
    
    Args:
        num_rbf (int): Number of radial basis functions
        cutoff (float): Cutoff distance for interactions
    """
    
    def __init__(self, num_rbf, cutoff):
        super().__init__()
        self.num_rbf = num_rbf
        self.cutoff = cutoff

        # Fixed centers and widths for Gaussian RBFs
        self.centers = nn.Parameter(
            torch.linspace(0, cutoff, num_rbf), 
            requires_grad=False
        )
        self.widths = nn.Parameter(
            (cutoff / num_rbf) * torch.ones(num_rbf), 
            requires_grad=False
        )

    def forward(self, dist):
        """
        Apply radial basis functions to distances.
        
        Args:
            dist (torch.Tensor): Distances with shape [num_edges, 1]
            
        Returns:
            torch.Tensor: RBF features with shape [num_edges, num_rbf]
        """
        # Apply cutoff
        dist = dist.clamp(max=self.cutoff)

        # Expand distances for vectorized computation
        dist_expanded = dist.expand(-1, self.num_rbf)
        
        # Compute Gaussian RBFs
        rbf = torch.exp(
            -((dist_expanded - self.centers.view(1, -1)) / 
              self.widths.view(1, -1))**2
        )

        # Apply smooth cutoff envelope
        envelope = self._envelope(dist)
        envelope_expanded = envelope.expand_as(rbf)
        
        return rbf * envelope_expanded

    def _envelope(self, dist):
        """
        Smooth cutoff envelope function.
        
        Args:
            dist (torch.Tensor): Distances
            
        Returns:
            torch.Tensor: Envelope values
        """
        return 1 - (dist / self.cutoff)**2


class E3EquivariantCGCNNConv(nn.Module):
    """
    E(3)-equivariant convolution layer for crystal graph networks.
    
    This layer performs message passing while preserving E(3) equivariance
    through the use of spherical harmonics and tensor products.
    
    Args:
        channels (int): Number of feature channels
        num_rbf (int): Number of radial basis functions
        cutoff (float): Cutoff distance for interactions
        lmax (int): Maximum spherical harmonic degree
    """
    
    def __init__(self, channels, num_rbf=16, cutoff=10.0, lmax=1):
        super().__init__()
        self.channels = channels
        self.num_rbf = num_rbf
        self.cutoff = cutoff
        self.lmax = lmax

        # Radial basis functions (invariant to rotations)
        self.rbf = RadialBasisLayer(num_rbf, cutoff)

        # Scalar network (invariant)
        self.scalar_mlp = nn.Sequential(
            nn.Linear(channels * 2 + num_rbf, channels),
            nn.SiLU(),
            nn.Linear(channels, channels)
        )

        # Equivariant vector operations
        # Input irreps: scalar (l=0) + vector (l=1)
        irreps_in1 = o3.Irreps(f"{channels}x0e + {channels}x1e")
        # Spherical harmonics irreps
        irreps_in2 = o3.Irreps([(1, (l, 1)) for l in range(lmax + 1)])
        # Output irreps: same structure as input1
        irreps_out = o3.Irreps(f"{channels}x0e + {channels}x1e")

        # Tensor product for equivariant message passing
        self.tp = FullyConnectedTensorProduct(
            irreps_in1=irreps_in1,
            irreps_in2=irreps_in2,
            irreps_out=irreps_out,
            internal_weights=True
        )

        # Activation functions
        self.scalar_act = nn.SiLU()
        self.gate_act = nn.Sigmoid()

    def forward(self, x_scalar, x_vector, edge_index, edge_attr, pos):
        """
        Forward pass of the E(3)-equivariant convolution.
        
        Args:
            x_scalar (torch.Tensor): Scalar node features [num_nodes, channels]
            x_vector (torch.Tensor): Vector node features [num_nodes, channels, 3]
            edge_index (torch.Tensor): Edge connectivity [2, num_edges]
            edge_attr (torch.Tensor): Edge attributes [num_edges, edge_dim]
            pos (torch.Tensor): Node positions [num_nodes, 3]
            
        Returns:
            tuple: Updated (x_scalar, x_vector) features
        """
        row, col = edge_index

        # Calculate distances (invariant)
        dist = torch.norm(pos[row] - pos[col], dim=-1, keepdim=True)

        # Calculate unit vectors (equivariant)
        edge_vec = pos[row] - pos[col]

        # Apply radial basis functions (invariant)
        rbf_output = self.rbf(dist)

        # Process scalar features (invariant path)
        scalar_message_input = torch.cat([
            x_scalar[row],
            x_scalar[col],
            rbf_output
        ], dim=-1)

        scalar_message = self.scalar_mlp(scalar_message_input)

        # Compute spherical harmonics (equivariant)
        edge_sh = spherical_harmonics(
            list(range(self.lmax + 1)),
            edge_vec / (dist + 1e-8),
            normalize=True
        )

        # Combine scalar and vector features for equivariant processing
        src_features = torch.cat([
            x_scalar[row], 
            x_vector[row].reshape(x_vector[row].shape[0], -1)
        ], dim=-1)

        # Apply tensor product for equivariant message passing
        message = self.tp(src_features, edge_sh)

        # Aggregate messages (sum is equivariant)
        scalar_out = scatter(
            scalar_message, col, dim=0, 
            dim_size=x_scalar.size(0), reduce='add'
        )
        vector_out = scatter(
            message[:, self.channels:].view(-1, self.channels, 3), 
            col, dim=0, dim_size=x_vector.size(0), reduce='add'
        )

        # Apply activations manually
        scalar_out = self.scalar_act(scalar_out)
        gates = self.gate_act(scalar_out)
        gated_vectors = vector_out * gates.unsqueeze(-1)

        # Residual connections
        x_scalar_new = x_scalar + scalar_out
        x_vector_new = x_vector + gated_vectors

        return x_scalar_new, x_vector_new
