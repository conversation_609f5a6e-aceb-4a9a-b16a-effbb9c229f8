#!/usr/bin/env python3
"""
Quick test script to verify ALIGNN baseline setup.

This script performs basic tests to ensure the ALIGNN implementation
is working correctly with the CGN-E3 dataset format.
"""

import sys
import torch
import numpy as np
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test that all ALIGNN modules can be imported."""
    print("Testing imports...")
    
    try:
        from alignn_baseline import ALIGNNDataset, ALIGNN, create_line_graph
        from alignn_baseline.utils import collate_alignn_batch
        print("✓ All imports successful")
        return True
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False


def test_line_graph_creation():
    """Test line graph creation functionality."""
    print("Testing line graph creation...")
    
    try:
        from alignn_baseline.data.line_graph import create_line_graph
        from torch_geometric.data import Data
        
        # Create a simple test graph
        x = torch.randn(4, 8)
        edge_index = torch.tensor([[0, 1, 2, 3], [1, 2, 3, 0]], dtype=torch.long)
        edge_attr = torch.randn(4, 4)
        pos = torch.randn(4, 3)
        
        data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr, pos=pos)
        
        # Create line graph
        atomic_graph, line_graph = create_line_graph(data)
        
        print(f"  Original graph: {atomic_graph.num_nodes} nodes, {atomic_graph.edge_index.size(1)} edges")
        print(f"  Line graph: {line_graph.num_nodes} nodes, {line_graph.edge_index.size(1)} edges")
        print("✓ Line graph creation successful")
        return True
        
    except Exception as e:
        print(f"✗ Line graph creation failed: {e}")
        return False


def test_model_creation():
    """Test ALIGNN model creation and forward pass."""
    print("Testing model creation...")
    
    try:
        from alignn_baseline.models import ALIGNN
        from torch_geometric.data import Data
        
        # Create model
        model = ALIGNN(
            node_features=8,
            edge_features=4,
            hidden_channels=16,
            num_layers=2,
            num_classes=1
        )
        
        print(f"  Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # Test forward pass
        atomic_x = torch.randn(5, 8)
        atomic_edge_index = torch.tensor([[0, 1, 2], [1, 2, 0]], dtype=torch.long)
        atomic_edge_attr = torch.randn(3, 4)
        atomic_pos = torch.randn(5, 3)
        batch = torch.zeros(5, dtype=torch.long)
        
        atomic_graph = Data(
            x=atomic_x,
            edge_index=atomic_edge_index,
            edge_attr=atomic_edge_attr,
            pos=atomic_pos,
            num_nodes=5
        )
        
        line_x = torch.randn(3, 4)
        line_edge_index = torch.tensor([[0, 1], [1, 0]], dtype=torch.long)
        line_edge_attr = torch.randn(2, 4)
        
        line_graph = Data(
            x=line_x,
            edge_index=line_edge_index,
            edge_attr=line_edge_attr,
            num_nodes=3
        )
        
        batch_data = {
            'graph': atomic_graph,
            'line_graph': line_graph,
            'batch': batch
        }
        
        # Forward pass
        with torch.no_grad():
            output = model(batch_data)
        
        print(f"  Output shape: {output.shape}")
        print("✓ Model creation and forward pass successful")
        return True
        
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return False


def test_data_utilities():
    """Test data utility functions."""
    print("Testing data utilities...")
    
    try:
        from alignn_baseline.utils.data_utils import collate_alignn_batch
        from torch_geometric.data import Data
        
        # Create sample batch
        samples = []
        for i in range(2):
            atomic_graph = Data(
                x=torch.randn(3, 8),
                edge_index=torch.tensor([[0, 1], [1, 2]], dtype=torch.long),
                edge_attr=torch.randn(2, 4),
                pos=torch.randn(3, 3),
                num_nodes=3
            )
            
            line_graph = Data(
                x=torch.randn(2, 4),
                edge_index=torch.tensor([[0], [1]], dtype=torch.long),
                edge_attr=torch.randn(1, 4),
                num_nodes=2
            )
            
            sample = {
                'graph': atomic_graph,
                'line_graph': line_graph,
                'target': torch.tensor([float(i)]),
                'material_id': f'test_{i}'
            }
            samples.append(sample)
        
        # Test collation
        batch = collate_alignn_batch(samples)
        
        print(f"  Batched graphs: {batch['graph'].num_nodes} total nodes")
        print(f"  Batched targets: {batch['target'].shape}")
        print("✓ Data utilities successful")
        return True
        
    except Exception as e:
        print(f"✗ Data utilities failed: {e}")
        return False


def main():
    """Run all tests."""
    print("ALIGNN Baseline Setup Test")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_line_graph_creation,
        test_model_creation,
        test_data_utilities
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! ALIGNN baseline is ready to use.")
        return True
    else:
        print("✗ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
