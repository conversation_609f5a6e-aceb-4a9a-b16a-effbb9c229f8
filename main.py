#!/usr/bin/env python3
"""
Main execution script for CGN-E3: E(3)-Equivariant Crystal Graph Network with Capsule Networks.

This script provides a command-line interface for training and evaluating
E(3)-equivariant crystal graph networks on materials property prediction tasks.
"""

import argparse
import json
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from cgn_e3.utils.pipeline import run_spatial_gnn_capsnet, load_trained_model
from cgn_e3.utils.evaluation import evaluate_spatial_gnn
from cgn_e3.data import CartesianGraphDataset
import torch
from torch.utils.data import DataLoader
from torch_geometric.data import Batch


def load_config(config_path):
    """Load configuration from JSON file."""
    with open(config_path, 'r') as f:
        return json.load(f)


def main():
    parser = argparse.ArgumentParser(
        description="Train and evaluate CGN-E3 models for crystal property prediction"
    )
    
    # Required arguments
    parser.add_argument(
        "--dataset_path", 
        type=str, 
        required=True,
        help="Path to dataset directory containing NPZ, JSON, and CSV files"
    )
    parser.add_argument(
        "--target_name", 
        type=str, 
        required=True,
        help="Name of target property to predict (column name in CSV)"
    )
    
    # Training arguments
    parser.add_argument(
        "--epochs", 
        type=int, 
        default=200,
        help="Number of training epochs (default: 200)"
    )
    parser.add_argument(
        "--config", 
        type=str, 
        default="config/default_config.json",
        help="Path to model configuration file"
    )
    parser.add_argument(
        "--output_dir", 
        type=str, 
        default=None,
        help="Output directory for results (default: results_{target_name})"
    )
    
    # Model loading
    parser.add_argument(
        "--pretrained_model", 
        type=str, 
        default=None,
        help="Path to pretrained model checkpoint"
    )
    parser.add_argument(
        "--eval_only", 
        action="store_true",
        help="Only evaluate pretrained model (requires --pretrained_model)"
    )
    
    # Additional options
    parser.add_argument(
        "--seed", 
        type=int, 
        default=42,
        help="Random seed for reproducibility (default: 42)"
    )
    parser.add_argument(
        "--device", 
        type=str, 
        default="auto",
        choices=["auto", "cpu", "cuda"],
        help="Device to use for training/evaluation (default: auto)"
    )
    
    args = parser.parse_args()
    
    # Set random seed
    torch.manual_seed(args.seed)
    
    # Set device
    if args.device == "auto":
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    print(f"Using device: {device}")
    print(f"Random seed: {args.seed}")
    
    # Load configuration
    if os.path.exists(args.config):
        config = load_config(args.config)
        print(f"Loaded configuration from {args.config}")
    else:
        print(f"Configuration file {args.config} not found, using defaults")
        config = {}
    
    # Set output directory
    if args.output_dir is None:
        args.output_dir = f"results_{args.target_name}"
    
    print(f"Results will be saved to: {args.output_dir}")
    
    # Evaluation only mode
    if args.eval_only:
        if args.pretrained_model is None:
            print("Error: --eval_only requires --pretrained_model")
            sys.exit(1)
        
        print("Evaluation-only mode")
        
        # Load dataset for evaluation
        dataset = CartesianGraphDataset(args.dataset_path, target_name=args.target_name)
        test_loader = DataLoader(
            dataset, batch_size=64, shuffle=False, collate_fn=Batch.from_data_list
        )
        
        # Load model
        model_config = {
            'node_features': dataset[0].x.size(1),
            'edge_features': dataset[0].edge_attr.size(1),
            **config.get('model', {})
        }
        
        model = load_trained_model(args.pretrained_model, model_config, device)
        
        # Evaluate
        results_path = os.path.join(args.output_dir, f"eval_metrics_{args.target_name}.csv")
        os.makedirs(args.output_dir, exist_ok=True)
        
        results = evaluate_spatial_gnn(model, test_loader, device, results_path)
        
        print(f"\nEvaluation Results:")
        print(f"MSE: {results['mse']:.6f}")
        print(f"RMSE: {results['rmse']:.6f}")
        print(f"MAE: {results['mae']:.6f}")
        print(f"R²: {results['r2']:.6f}")
        
        return
    
    # Training mode
    print("Starting training pipeline...")
    print(f"Dataset: {args.dataset_path}")
    print(f"Target: {args.target_name}")
    print(f"Epochs: {args.epochs}")
    
    if args.pretrained_model:
        print(f"Pretrained model: {args.pretrained_model}")
    
    # Run training pipeline
    model, results = run_spatial_gnn_capsnet(
        dataset_path=args.dataset_path,
        target_name=args.target_name,
        epochs=args.epochs,
        pretrained_model_path=args.pretrained_model,
        output_dir=args.output_dir,
        model_config=config.get('model', {})
    )
    
    # Print final results
    print("\n" + "="*50)
    print("FINAL TEST RESULTS")
    print("="*50)
    print(f"MSE:  {results['mse']:.6f}")
    print(f"RMSE: {results['rmse']:.6f}")
    print(f"MAE:  {results['mae']:.6f}")
    print(f"R²:   {results['r2']:.6f}")
    print("="*50)
    
    print(f"\nAll results saved to: {args.output_dir}")


if __name__ == "__main__":
    main()
