# CGN-E3 Examples

This directory contains example scripts demonstrating how to use the CGN-E3 library for various tasks.

## Examples

### 1. Basic Usage (`basic_usage.py`)

Demonstrates the fundamental workflow for training and evaluating a CGN-E3 model:

- Loading a dataset
- Creating and configuring a model
- Training with validation
- Evaluating on test data

```bash
python examples/basic_usage.py
```

**Note**: Update the `dataset_path` variable in the script to point to your actual dataset.

### 2. Hyperparameter Tuning (`hyperparameter_tuning.py`)

Shows how to perform systematic hyperparameter search:

- Defines a grid of hyperparameters to test
- Trains models with different configurations
- Compares results and identifies the best configuration
- Saves results for analysis

```bash
python examples/hyperparameter_tuning.py
```

This will create a `hyperparameter_search/` directory with results from each configuration and save the best configuration to `best_config.json`.

## Dataset Requirements

All examples expect a dataset directory containing:

1. **BandgapTargets.npz**: Graph structure data
2. **BandgapTargets_config.json**: Configuration file with atomic numbers and node vectors
3. **BandgapTargets.csv**: Target property values

## Customization

### Modifying Model Architecture

You can easily modify the model architecture by changing the parameters in the model initialization:

```python
model = E3EquivariantCrystalGNNCapsNet(
    node_features=dataset[0].x.size(1),
    edge_features=dataset[0].edge_attr.size(1),
    hidden_channels=256,        # Increase for more capacity
    num_conv_layers=3,          # More layers for deeper networks
    primary_caps=8,             # More capsules for complex features
    primary_dim=16,             # Higher dimensional capsules
    secondary_caps=6,           # Secondary capsule configuration
    secondary_dim=16,
    dropout_rate=0.05           # Regularization
)
```

### Training Configuration

Adjust training parameters for your specific needs:

```python
# Optimizer settings
optimizer = torch.optim.AdamW(
    model.parameters(), 
    lr=1e-3,                    # Learning rate
    weight_decay=1e-4           # L2 regularization
)

# Learning rate scheduler
scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
    optimizer, 
    mode='min', 
    factor=0.5,                 # Reduction factor
    patience=15,                # Epochs to wait
    verbose=True
)

# Training parameters
trained_model, _, _, _ = train_spatial_gnn(
    model, train_loader, optimizer, device,
    epochs=200,                 # Training epochs
    scheduler=scheduler,
    early_stopping_patience=50, # Early stopping patience
    checkpoint_path="model.pt",
    metrics_path="metrics.csv"
)
```

### Using Configuration Files

You can also use the configuration files in the `config/` directory:

```python
import json

# Load configuration
with open('config/default_config.json', 'r') as f:
    config = json.load(f)

# Use configuration
model = E3EquivariantCrystalGNNCapsNet(
    node_features=dataset[0].x.size(1),
    edge_features=dataset[0].edge_attr.size(1),
    **config['model']
)
```

## Performance Tips

1. **GPU Usage**: The examples automatically detect and use GPU if available
2. **Batch Size**: Adjust batch size based on your GPU memory
3. **Model Size**: Start with smaller models for initial experiments
4. **Early Stopping**: Use early stopping to prevent overfitting
5. **Learning Rate**: Use learning rate scheduling for better convergence

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce batch size or model size
2. **Slow Training**: Ensure you're using GPU and appropriate batch size
3. **Poor Performance**: Try different hyperparameters or increase model capacity
4. **Dataset Errors**: Verify dataset format and file paths

### Getting Help

If you encounter issues:

1. Check the main README.md for installation instructions
2. Verify your dataset format matches the expected structure
3. Try the smaller model configurations first
4. Check the test suite for working examples

## Next Steps

After running these examples:

1. Experiment with different model architectures
2. Try different target properties
3. Implement custom evaluation metrics
4. Add visualization of results
5. Explore transfer learning between different properties
