"""
Setup script for CGN-E3: E(3)-Equivariant Crystal Graph Network with Capsule Networks
"""

from setuptools import setup, find_packages
import os

# Read README for long description
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="cgn-e3",
    version="0.1.0",
    author="CGN-E3 Team",
    author_email="<EMAIL>",
    description="E(3)-Equivariant Crystal Graph Network with Capsule Networks",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/cgn-e3",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Chemistry",
        "Topic :: Scientific/Engineering :: Physics",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=6.2.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "jupyter>=1.0.0",
        ],
        "viz": [
            "matplotlib>=3.5.0",
            "seaborn>=0.11.0",
            "plotly>=5.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "cgn-e3=cgn_e3.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "cgn_e3": ["config/*.json"],
    },
    keywords="materials science, graph neural networks, equivariance, capsule networks, crystal property prediction",
    project_urls={
        "Bug Reports": "https://github.com/your-username/cgn-e3/issues",
        "Source": "https://github.com/your-username/cgn-e3",
        "Documentation": "https://cgn-e3.readthedocs.io/",
    },
)
