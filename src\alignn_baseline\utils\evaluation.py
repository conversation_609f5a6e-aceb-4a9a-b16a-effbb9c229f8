"""
Evaluation utilities for ALIGNN model.

This module provides evaluation functions and metrics for the ALIGNN baseline model.
"""

import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, Any, List, Tuple, Optional
from tqdm import tqdm

from .data_utils import denormalize_targets
from .training import move_batch_to_device


def evaluate_alignn(model: nn.Module, data_loader: DataLoader, device: torch.device,
                   target_mean: float = 0.0, target_std: float = 1.0,
                   return_predictions: bool = False) -> Dict[str, Any]:
    """
    Evaluate ALIGNN model on a dataset.
    
    Args:
        model: Trained ALIGNN model
        data_loader: Data loader for evaluation
        device: Device to run evaluation on
        target_mean: Mean of targets for denormalization
        target_std: Standard deviation of targets for denormalization
        return_predictions: Whether to return predictions and targets
        
    Returns:
        Dictionary containing evaluation metrics
    """
    model.eval()
    model = model.to(device)
    
    all_predictions = []
    all_targets = []
    all_material_ids = []
    total_loss = 0.0
    num_batches = 0
    
    criterion = nn.MSELoss()
    
    with torch.no_grad():
        pbar = tqdm(data_loader, desc="Evaluating")
        for batch in pbar:
            batch = move_batch_to_device(batch, device)
            
            # Forward pass
            predictions = model(batch)
            targets = batch['target']
            
            # Compute loss
            loss = criterion(predictions, targets)
            total_loss += loss.item()
            num_batches += 1
            
            # Denormalize predictions and targets
            pred_denorm = denormalize_targets(predictions, target_mean, target_std)
            target_denorm = denormalize_targets(targets, target_mean, target_std)
            
            # Store results
            all_predictions.extend(pred_denorm.cpu().numpy().flatten())
            all_targets.extend(target_denorm.cpu().numpy().flatten())
            all_material_ids.extend(batch['material_ids'])
            
            # Update progress bar
            mae = np.mean(np.abs(np.array(all_predictions) - np.array(all_targets)))
            pbar.set_postfix({'MAE': f"{mae:.4f}"})
    
    # Convert to numpy arrays
    predictions = np.array(all_predictions)
    targets = np.array(all_targets)
    
    # Compute metrics
    mae = mean_absolute_error(targets, predictions)
    mse = mean_squared_error(targets, predictions)
    rmse = np.sqrt(mse)
    r2 = r2_score(targets, predictions)
    
    # Additional metrics
    mape = np.mean(np.abs((targets - predictions) / (targets + 1e-8))) * 100
    max_error = np.max(np.abs(targets - predictions))
    
    # Compute percentile errors
    errors = np.abs(targets - predictions)
    percentile_50 = np.percentile(errors, 50)
    percentile_90 = np.percentile(errors, 90)
    percentile_95 = np.percentile(errors, 95)
    
    metrics = {
        'mae': mae,
        'mse': mse,
        'rmse': rmse,
        'r2': r2,
        'mape': mape,
        'max_error': max_error,
        'median_error': percentile_50,
        'p90_error': percentile_90,
        'p95_error': percentile_95,
        'avg_loss': total_loss / num_batches,
        'num_samples': len(predictions)
    }
    
    if return_predictions:
        metrics['predictions'] = predictions
        metrics['targets'] = targets
        metrics['material_ids'] = all_material_ids
    
    return metrics


def plot_predictions(targets: np.ndarray, predictions: np.ndarray, 
                    title: str = "ALIGNN Predictions vs Targets",
                    save_path: str = None, show_plot: bool = True) -> plt.Figure:
    """
    Plot predictions vs targets with error analysis.
    
    Args:
        targets: True target values
        predictions: Predicted values
        title: Plot title
        save_path: Path to save plot
        show_plot: Whether to display plot
        
    Returns:
        Matplotlib figure
    """
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle(title, fontsize=16)
    
    # Scatter plot
    ax1 = axes[0, 0]
    ax1.scatter(targets, predictions, alpha=0.6, s=20)
    
    # Perfect prediction line
    min_val = min(targets.min(), predictions.min())
    max_val = max(targets.max(), predictions.max())
    ax1.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Perfect Prediction')
    
    ax1.set_xlabel('True Values')
    ax1.set_ylabel('Predicted Values')
    ax1.set_title('Predictions vs Targets')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Add R² to the plot
    r2 = r2_score(targets, predictions)
    ax1.text(0.05, 0.95, f'R² = {r2:.3f}', transform=ax1.transAxes, 
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # Residual plot
    ax2 = axes[0, 1]
    residuals = predictions - targets
    ax2.scatter(targets, residuals, alpha=0.6, s=20)
    ax2.axhline(y=0, color='r', linestyle='--', lw=2)
    ax2.set_xlabel('True Values')
    ax2.set_ylabel('Residuals (Predicted - True)')
    ax2.set_title('Residual Plot')
    ax2.grid(True, alpha=0.3)
    
    # Error distribution
    ax3 = axes[1, 0]
    errors = np.abs(residuals)
    ax3.hist(errors, bins=50, alpha=0.7, edgecolor='black')
    ax3.set_xlabel('Absolute Error')
    ax3.set_ylabel('Frequency')
    ax3.set_title('Error Distribution')
    ax3.grid(True, alpha=0.3)
    
    # Add statistics
    mae = np.mean(errors)
    rmse = np.sqrt(np.mean(residuals**2))
    ax3.axvline(mae, color='red', linestyle='--', label=f'MAE = {mae:.3f}')
    ax3.legend()
    
    # Q-Q plot for residuals
    ax4 = axes[1, 1]
    from scipy import stats
    stats.probplot(residuals, dist="norm", plot=ax4)
    ax4.set_title('Q-Q Plot of Residuals')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    if show_plot:
        plt.show()
    
    return fig


def analyze_model_performance(metrics: Dict[str, Any], dataset_name: str = "Dataset") -> str:
    """
    Generate a performance analysis report.
    
    Args:
        metrics: Dictionary of evaluation metrics
        dataset_name: Name of the dataset
        
    Returns:
        Performance analysis report as string
    """
    report = f"""
ALIGNN Model Performance Analysis - {dataset_name}
{'='*60}

Overall Metrics:
- Mean Absolute Error (MAE): {metrics['mae']:.4f}
- Root Mean Square Error (RMSE): {metrics['rmse']:.4f}
- R² Score: {metrics['r2']:.4f}
- Mean Absolute Percentage Error (MAPE): {metrics['mape']:.2f}%

Error Distribution:
- Median Error: {metrics['median_error']:.4f}
- 90th Percentile Error: {metrics['p90_error']:.4f}
- 95th Percentile Error: {metrics['p95_error']:.4f}
- Maximum Error: {metrics['max_error']:.4f}

Dataset Information:
- Number of Samples: {metrics['num_samples']:,}
- Average Loss: {metrics['avg_loss']:.6f}

Performance Assessment:
"""
    
    # Performance assessment based on R²
    if metrics['r2'] >= 0.9:
        assessment = "Excellent"
    elif metrics['r2'] >= 0.8:
        assessment = "Good"
    elif metrics['r2'] >= 0.7:
        assessment = "Fair"
    else:
        assessment = "Poor"
    
    report += f"- Overall Performance: {assessment} (R² = {metrics['r2']:.3f})\n"
    
    # Error analysis
    if metrics['mape'] < 5:
        error_assessment = "Very Low"
    elif metrics['mape'] < 10:
        error_assessment = "Low"
    elif metrics['mape'] < 20:
        error_assessment = "Moderate"
    else:
        error_assessment = "High"
    
    report += f"- Error Level: {error_assessment} (MAPE = {metrics['mape']:.1f}%)\n"
    
    return report


def compare_models(results_dict: Dict[str, Dict[str, Any]], 
                  save_path: str = None) -> plt.Figure:
    """
    Compare performance of multiple models.
    
    Args:
        results_dict: Dictionary with model names as keys and metrics as values
        save_path: Path to save comparison plot
        
    Returns:
        Matplotlib figure
    """
    models = list(results_dict.keys())
    metrics_to_compare = ['mae', 'rmse', 'r2', 'mape']
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle('Model Performance Comparison', fontsize=16)
    
    for i, metric in enumerate(metrics_to_compare):
        ax = axes[i // 2, i % 2]
        
        values = [results_dict[model][metric] for model in models]
        
        bars = ax.bar(models, values, alpha=0.7)
        ax.set_title(f'{metric.upper()}')
        ax.set_ylabel(metric.upper())
        
        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value:.3f}', ha='center', va='bottom')
        
        # Rotate x-axis labels if needed
        if len(max(models, key=len)) > 8:
            ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig
