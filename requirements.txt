# Core dependencies
torch>=1.12.0
torch-geometric>=2.1.0
e3nn>=0.5.0

# Scientific computing
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0

# Utilities
tqdm>=4.62.0
matplotlib>=3.5.0
seaborn>=0.11.0

# Development (optional)
pytest>=6.2.0
black>=22.0.0
flake8>=4.0.0
jupyter>=1.0.0

# For CUDA support (optional, install manually if needed)
# torch-scatter
# torch-sparse
# torch-cluster
# torch-spline-conv
